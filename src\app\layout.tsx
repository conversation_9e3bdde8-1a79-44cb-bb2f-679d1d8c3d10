import { AuthProvider } from '@/providers/AuthProvider';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { LanguageProvider } from '@/providers/LanguageProvider';
import { AuthGuard } from '@/components/auth/AuthGuard';
import './globals.css';

export const metadata = {
  title: 'Doctory - Healthcare Booking Platform',
  description: 'Book appointments with licensed doctors easily',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="min-h-screen font-sans antialiased bg-white text-black">
        {children}
      </body>
    </html>
  );
}
