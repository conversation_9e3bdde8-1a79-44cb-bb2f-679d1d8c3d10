-- Comprehensive Trigger Verification Script
-- Run this to verify the trigger is working correctly

-- 1. Check current state of all tables
SELECT '=== CURRENT STATE BEFORE TESTING ===' as status;

SELECT 'AUTH.USERS TABLE:' as table_name;
SELECT id, email, created_at, raw_user_meta_data FROM auth.users ORDER BY created_at DESC LIMIT 5;

SELECT 'PUBLIC.USERS TABLE:' as table_name;
SELECT id, email, role, created_at FROM public.users ORDER BY created_at DESC LIMIT 5;

SELECT 'PATIENTS TABLE:' as table_name;
SELECT id, user_id, name, phone, created_at FROM public.patients ORDER BY created_at DESC LIMIT 5;

SELECT 'DOCTORS TABLE:' as table_name;
SELECT id, user_id, name, specialty, experience, created_at FROM public.doctors ORDER BY created_at DESC LIMIT 5;

-- 2. Verify trigger exists and is active
SELECT '=== TRIGGER VERIFICATION ===' as status;

SELECT 
  tgname as trigger_name,
  tgrelid::regclass as table_name,
  tgfoid::regproc as function_name,
  tgenabled as enabled
FROM pg_trigger 
WHERE tgname = 'on_auth_user_created';

-- 3. Check if function exists
SELECT 
  proname as function_name,
  proowner::regrole as owner,
  prosecdef as security_definer
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- 4. Test the relationship between auth.users and public.users
SELECT '=== USER RELATIONSHIPS ===' as status;

SELECT 
  au.id,
  au.email as auth_email,
  au.created_at as auth_created,
  pu.email as public_email,
  pu.role as user_role,
  pu.created_at as public_created,
  CASE 
    WHEN pu.id IS NOT NULL THEN 'LINKED'
    ELSE 'MISSING'
  END as link_status
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
ORDER BY au.created_at DESC
LIMIT 10;

-- 5. Check patient/doctor profile creation
SELECT '=== PROFILE RELATIONSHIPS ===' as status;

SELECT 
  u.id as user_id,
  u.email,
  u.role,
  CASE 
    WHEN u.role = 'patient' AND p.user_id IS NOT NULL THEN 'PATIENT_PROFILE_EXISTS'
    WHEN u.role = 'patient' AND p.user_id IS NULL THEN 'PATIENT_PROFILE_MISSING'
    WHEN u.role = 'doctor' AND d.user_id IS NOT NULL THEN 'DOCTOR_PROFILE_EXISTS'
    WHEN u.role = 'doctor' AND d.user_id IS NULL THEN 'DOCTOR_PROFILE_MISSING'
    ELSE 'UNKNOWN_ROLE'
  END as profile_status,
  p.name as patient_name,
  d.name as doctor_name,
  d.specialty
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
LEFT JOIN public.doctors d ON u.id = d.user_id
ORDER BY u.created_at DESC;

-- 6. Instructions for testing
SELECT '
=== TESTING INSTRUCTIONS ===

The trigger function is now installed and ready to test.

TO TEST THE TRIGGER:
1. Go to your application signup page
2. Register a new user with role "patient"
3. Register a new user with role "doctor"
4. Run this verification script again to see the results

EXPECTED RESULTS:
- New users should appear in both auth.users and public.users
- Patient users should have records in public.patients table
- Doctor users should have records in public.doctors table
- All relationships should show as "LINKED" and profiles as "EXISTS"

IF TRIGGER IS NOT WORKING:
- Check if the trigger is enabled (should show "O" for enabled)
- Verify the function has SECURITY DEFINER privileges
- Check Supabase logs for any error messages
- Ensure your app is passing role in user metadata during signup

MANUAL TESTING QUERY:
After registering new users, run this to see the complete picture:

SELECT 
  u.email,
  u.role,
  p.name as patient_name,
  d.name as doctor_name,
  d.specialty,
  u.created_at
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id  
LEFT JOIN public.doctors d ON u.id = d.user_id
WHERE u.created_at > NOW() - INTERVAL ''1 hour''
ORDER BY u.created_at DESC;

' as instructions;
