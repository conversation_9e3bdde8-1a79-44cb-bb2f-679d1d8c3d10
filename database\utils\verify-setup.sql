-- Database Setup Verification Script
-- Run this script to verify that all tables and configurations are properly set up

-- Check if all required tables exist
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') 
    THEN '✓ users table exists' 
    ELSE '✗ users table missing' 
  END as users_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'patients' AND table_schema = 'public') 
    THEN '✓ patients table exists' 
    ELSE '✗ patients table missing' 
  END as patients_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'doctors' AND table_schema = 'public') 
    THEN '✓ doctors table exists' 
    ELSE '✗ doctors table missing' 
  END as doctors_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'availability' AND table_schema = 'public') 
    THEN '✓ availability table exists' 
    ELSE '✗ availability table missing' 
  END as availability_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'appointments' AND table_schema = 'public') 
    THEN '✓ appointments table exists' 
    ELSE '✗ appointments table missing' 
  END as appointments_table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_files' AND table_schema = 'public') 
    THEN '✓ medical_files table exists' 
    ELSE '✗ medical_files table missing' 
  END as medical_files_table_status;

-- Check if storage bucket exists
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'medical-files') 
    THEN '✓ medical-files bucket exists' 
    ELSE '✗ medical-files bucket missing' 
  END as storage_bucket_status;

-- Check if sample data exists
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM public.doctors LIMIT 1) 
    THEN '✓ Sample doctors data exists' 
    ELSE '✗ No sample doctors data' 
  END as sample_data_status;

-- List all tables in public schema
SELECT 'Available tables in public schema:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- List all storage buckets
SELECT 'Available storage buckets:' as info;
SELECT name FROM storage.buckets ORDER BY name;

-- Check RLS status
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Verification complete message
SELECT 'Database verification completed!' as message;