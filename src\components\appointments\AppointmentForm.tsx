'use client';

import React, { useState } from 'react';
import { Button } from '../ui/Button';

type AppointmentFormProps = {
  doctorId: string;
  onSubmit: (date: string, time: string) => Promise<void>;
  onCancel: () => void;
  className?: string;
};

// Simple translations for this component
const translations = {
  selectDate: 'Select Date',
  selectTime: 'Select Time',
  bookAppointment: 'Book Appointment',
  cancel: 'Cancel',
  booking: 'Booking...',
  pleaseSelectDateTime: 'Please select both date and time',
  bookingFailed: 'Failed to book appointment. Please try again.',
};

export const AppointmentForm: React.FC<AppointmentFormProps> = ({
  doctorId,
  onSubmit,
  onCancel,
  className = ''
}) => {
  const t = (key: string) => translations[key as keyof typeof translations] || key;
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!date || !time) {
      setError('Please select both date and time');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onSubmit(date, time);
    } catch (err) {
      setError('Failed to book appointment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={`bg-card rounded-lg shadow-sm overflow-hidden border border-border p-4 ${className}`}>
      <h3 className="text-lg font-medium mb-4">{t('bookAppointment')}</h3>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="date" className="block text-sm font-medium mb-1">
              {t('selectDate')}
            </label>
            <input
              id="date"
              type="date"
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              required
            />
          </div>
          
          <div>
            <label htmlFor="time" className="block text-sm font-medium mb-1">
              {t('selectTime')}
            </label>
            <select
              id="time"
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              value={time}
              onChange={(e) => setTime(e.target.value)}
              required
            >
              <option value="">{t('selectTime')}</option>
              <option value="09:00">09:00 AM</option>
              <option value="10:00">10:00 AM</option>
              <option value="11:00">11:00 AM</option>
              <option value="12:00">12:00 PM</option>
              <option value="13:00">01:00 PM</option>
              <option value="14:00">02:00 PM</option>
              <option value="15:00">03:00 PM</option>
              <option value="16:00">04:00 PM</option>
              <option value="17:00">05:00 PM</option>
            </select>
          </div>
          
          {error && (
            <div className="text-sm text-red-500">
              {error}
            </div>
          )}
          
          <div className="flex justify-end space-x-2 rtl:space-x-reverse pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              isLoading={isLoading}
              disabled={isLoading}
            >
              {t('confirm')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};
