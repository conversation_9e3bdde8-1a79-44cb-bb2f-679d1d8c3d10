# Supabase Project Re-initialization Guide

## 1. Update Environment Variables

Update your `.env.local` file with your new Supabase project credentials:

```
NEXT_PUBLIC_SUPABASE_URL=your_new_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_new_supabase_anon_key
```

You can find these values in your Supabase project dashboard under Project Settings > API.

## 2. Install Updated Packages

Run the following command to install the required Supabase packages:

```bash
npm install @supabase/ssr @supabase/supabase-js
```

## 3. Database Setup

Create the following tables in your Supabase database:

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('patient', 'doctor', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Patients Table
```sql
CREATE TABLE patients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Doctors Table
```sql
CREATE TABLE doctors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  specialty TEXT NOT NULL,
  experience INTEGER NOT NULL DEFAULT 0,
  bio TEXT,
  photo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Availability Table
```sql
CREATE TABLE availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6),
  start_time TEXT NOT NULL,
  end_time TEXT NOT NULL,
  is_recurring BOOLEAN DEFAULT TRUE,
  specific_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CHECK ((is_recurring = TRUE AND day_of_week IS NOT NULL) OR 
         (is_recurring = FALSE AND specific_date IS NOT NULL))
);
```

### Appointments Table
```sql
CREATE TABLE appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  time TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'confirmed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Medical Files Table
```sql
CREATE TABLE medical_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
  appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 4. Set Up Row Level Security (RLS) Policies

Implement these RLS policies to secure your data:

### Users Table
```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Admin can see all users
CREATE POLICY "Admins can see all users" ON users
  FOR SELECT USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin'));

-- Users can see their own data
CREATE POLICY "Users can see own data" ON users
  FOR SELECT USING (auth.uid() = id);

-- Only the system can insert users (handled by triggers or server functions)
CREATE POLICY "System can insert users" ON users
  FOR INSERT WITH CHECK (true);
```

### Patients Table
```sql
-- Enable RLS
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;

-- Patients can see their own data
CREATE POLICY "Patients can see own data" ON patients
  FOR SELECT USING (auth.uid() = user_id);

-- Doctors can see patients who have appointments with them
CREATE POLICY "Doctors can see patients with appointments" ON patients
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appointments a
      JOIN doctors d ON a.doctor_id = d.id
      WHERE a.patient_id = patients.id AND d.user_id = auth.uid()
    )
  );

-- Admin can see all patients
CREATE POLICY "Admins can see all patients" ON patients
  FOR SELECT USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin'));

-- System can insert patients
CREATE POLICY "System can insert patients" ON patients
  FOR INSERT WITH CHECK (true);
```

## 5. Create Auth Triggers

Create a trigger to automatically add new users to the users table:

```sql
-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, role)
  VALUES (new.id, new.email, 'patient');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function on new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

## 6. Storage Buckets

Create a storage bucket for medical files:

1. Go to Storage in your Supabase dashboard
2. Create a new bucket called `medical_files`
3. Set the privacy to private
4. Add the following RLS policy:

```sql
-- Patients can upload their own files
CREATE POLICY "Patients can upload their own files"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'medical_files' AND
  auth.uid() IN (SELECT user_id FROM patients)
);

-- Patients can view their own files
CREATE POLICY "Patients can view their own files"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'medical_files' AND
  (storage.foldername(name))[1] = auth.uid()::text
);

-- Doctors can view files for their patients
CREATE POLICY "Doctors can view patient files"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'medical_files' AND
  auth.uid() IN (
    SELECT d.user_id FROM doctors d
    JOIN appointments a ON d.id = a.doctor_id
    JOIN patients p ON a.patient_id = p.id
    WHERE (storage.foldername(name))[1] = p.user_id::text
  )
);
```

## 7. Restart Your Development Server

After making all these changes, restart your development server:

```bash
npm run dev
```