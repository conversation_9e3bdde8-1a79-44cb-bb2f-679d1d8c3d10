import { test, expect } from '@playwright/test';

// Test suite for authentication error handling
test.describe('Authentication Error Handling', () => {
  // Test for registration error handling
  test('should handle registration errors properly', async ({ page }) => {
    // Navigate to register page
    await page.goto('http://localhost:3000/auth/register');
    
    // Fill in registration form with an email that might cause an error
    // (e.g., an email that's already registered)
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password123!');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if error message is displayed with specific details
    await expect(page.locator('.bg-red-100')).toBeVisible();
    
    // The error message should be descriptive, not just "An error occurred"
    await expect(page.locator('.bg-red-100')).not.toHaveText('An error occurred');
  });

  // Test for database connection errors
  test('should handle database connection errors', async ({ page }) => {
    // This test simulates a database connection error by using an invalid email format
    // that might trigger a validation error in Supabase
    await page.goto('http://localhost:3000/auth/register');
    
    // Fill in registration form with an invalid email format
    await page.fill('input[type="email"]', 'not-an-email');
    await page.fill('input[type="password"]', 'Password123!');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if error message is displayed
    await expect(page.locator('.bg-red-100')).toBeVisible();
  });

  // Test for password strength validation
  test('should validate password strength', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/register');
    
    // Fill in registration form with a weak password
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '123');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if error message about password strength is displayed
    await expect(page.locator('.bg-red-100')).toBeVisible();
    await expect(page.locator('.bg-red-100')).toContainText(/password/i);
  });
});