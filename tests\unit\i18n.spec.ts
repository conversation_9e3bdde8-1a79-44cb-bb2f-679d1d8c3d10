import { test, expect } from '@playwright/test';

test.describe('Internationalization', () => {
  // Test English language display
  test('should display content in English by default', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // Check that text is in English
    await expect(page.locator('h2')).toContainText('Sign In');
    await expect(page.locator('button[type="submit"]')).toContainText('Sign In');
    await expect(page.locator('label[for="email"]')).toContainText('Email');
    await expect(page.locator('label[for="password"]')).toContainText('Password');
  });
  
  // Test Arabic language display (if language switcher is implemented)
  test('should switch to Arabic when language is changed', async ({ page }) => {
    // This test assumes there's a language switcher implemented
    // If not implemented yet, this test will fail and should be skipped
    test.skip(true, 'Language switcher not implemented yet');
    
    await page.goto('http://localhost:3000/auth/login');
    
    // Click on language switcher (assuming it exists)
    await page.click('button[aria-label="Switch to Arabic"]');
    
    // Check that text is in Arabic
    await expect(page.locator('h2')).toContainText('تسجيل الدخول');
    await expect(page.locator('button[type="submit"]')).toContainText('تسجيل الدخول');
    await expect(page.locator('label[for="email"]')).toContainText('البريد الإلكتروني');
    await expect(page.locator('label[for="password"]')).toContainText('كلمة المرور');
  });
});