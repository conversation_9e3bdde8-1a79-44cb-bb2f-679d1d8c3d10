-- Verify Patient Profiles and Fix Login Issues
-- Run this to check if patient profiles were created correctly

-- 1. Check patients table
SELECT '=== PATIENTS TABLE ===' as section;
SELECT id, user_id, name, phone, created_at FROM public.patients ORDER BY created_at DESC;

-- 2. Check complete user-patient relationship
SELECT '=== USER-PATIENT RELATIONSHIPS ===' as section;
SELECT 
  u.id as user_id,
  u.email,
  u.role,
  u.created_at as user_created,
  p.id as patient_id,
  p.name as patient_name,
  p.phone,
  p.created_at as patient_created,
  CASE 
    WHEN p.user_id IS NOT NULL THEN 'PROFILE_EXISTS'
    ELSE 'PROFILE_MISSING'
  END as profile_status
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
ORDER BY u.created_at DESC;

-- 3. Check for any missing patient profiles and create them
INSERT INTO public.patients (user_id, name, phone, created_at, updated_at)
SELECT 
  u.id,
  split_part(u.email, '@', 1) as name,
  '' as phone,
  NOW(),
  NOW()
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
WHERE u.role = 'patient' AND p.user_id IS NULL;

-- 4. Verify all patients now have profiles
SELECT '=== VERIFICATION AFTER FIX ===' as section;
SELECT 
  u.email,
  u.role,
  p.name,
  CASE 
    WHEN p.user_id IS NOT NULL THEN 'PROFILE_EXISTS'
    ELSE 'PROFILE_MISSING'
  END as status
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
WHERE u.role = 'patient'
ORDER BY u.created_at DESC;

-- 5. Test query that your app might be using for login
SELECT '=== LOGIN TEST QUERY ===' as section;
SELECT 
  u.id,
  u.email,
  u.role,
  p.name as patient_name,
  p.phone
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
WHERE u.email = '<EMAIL>';

-- 6. Count summary
SELECT '=== SUMMARY ===' as section;
SELECT 
  'Total users' as metric,
  COUNT(*) as count
FROM public.users
UNION ALL
SELECT 
  'Total patients' as metric,
  COUNT(*) as count
FROM public.patients
UNION ALL
SELECT 
  'Users with patient profiles' as metric,
  COUNT(*) as count
FROM public.users u
JOIN public.patients p ON u.id = p.user_id
WHERE u.role = 'patient';
