#!/usr/bin/env node

/**
 * Database Verification Script for Doctory Healthcare Platform
 * 
 * This script verifies that the database schema is properly set up
 * and all tables, policies, and sample data are in place.
 * 
 * Usage: node scripts/verify-database.js
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyTables() {
  console.log('🔍 Verifying database tables...');
  
  const expectedTables = ['users', 'patients', 'doctors', 'availability', 'appointments', 'medical_files'];
  const results = {};
  
  for (const table of expectedTables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      if (error) {
        results[table] = `❌ Error: ${error.message}`;
      } else {
        results[table] = '✅ Table exists and accessible';
      }
    } catch (err) {
      results[table] = `❌ Exception: ${err.message}`;
    }
  }
  
  console.log('\n📊 Table Verification Results:');
  for (const [table, status] of Object.entries(results)) {
    console.log(`   ${table}: ${status}`);
  }
  
  return Object.values(results).every(status => status.includes('✅'));
}

async function verifySampleData() {
  console.log('\n🔍 Verifying sample data...');
  
  try {
    const { data: doctors, error } = await supabase
      .from('doctors')
      .select('name, specialty');
    
    if (error) {
      console.log('❌ Could not fetch sample doctors:', error.message);
      return false;
    }
    
    if (doctors && doctors.length > 0) {
      console.log('✅ Sample doctors found:');
      doctors.forEach(doctor => {
        console.log(`   - ${doctor.name} (${doctor.specialty})`);
      });
      return true;
    } else {
      console.log('⚠️  No sample doctors found');
      return false;
    }
  } catch (err) {
    console.log('❌ Error checking sample data:', err.message);
    return false;
  }
}

async function verifyStorage() {
  console.log('\n🔍 Verifying storage setup...');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log('❌ Could not list storage buckets:', error.message);
      return false;
    }
    
    const medicalFilesBucket = buckets.find(bucket => bucket.id === 'medical_files');
    
    if (medicalFilesBucket) {
      console.log('✅ Medical files storage bucket exists');
      console.log(`   - Bucket ID: ${medicalFilesBucket.id}`);
      console.log(`   - Public: ${medicalFilesBucket.public}`);
      return true;
    } else {
      console.log('❌ Medical files storage bucket not found');
      return false;
    }
  } catch (err) {
    console.log('❌ Error checking storage:', err.message);
    return false;
  }
}

async function verifyAuth() {
  console.log('\n🔍 Verifying authentication setup...');
  
  try {
    // Test if we can access auth-related functionality
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Auth setup issue:', error.message);
      return false;
    }
    
    console.log('✅ Authentication system accessible');
    return true;
  } catch (err) {
    console.log('❌ Error checking auth:', err.message);
    return false;
  }
}

async function runVerification() {
  console.log('🚀 Starting Doctory database verification...\n');
  
  const results = {
    tables: await verifyTables(),
    sampleData: await verifySampleData(),
    storage: await verifyStorage(),
    auth: await verifyAuth()
  };
  
  console.log('\n📋 Verification Summary:');
  console.log(`   Tables: ${results.tables ? '✅ Pass' : '❌ Fail'}`);
  console.log(`   Sample Data: ${results.sampleData ? '✅ Pass' : '⚠️  Warning'}`);
  console.log(`   Storage: ${results.storage ? '✅ Pass' : '❌ Fail'}`);
  console.log(`   Authentication: ${results.auth ? '✅ Pass' : '❌ Fail'}`);
  
  const criticalTests = [results.tables, results.storage, results.auth];
  const allCriticalPassed = criticalTests.every(test => test);
  
  if (allCriticalPassed) {
    console.log('\n🎉 Database verification completed successfully!');
    console.log('Your Doctory database is ready for development.');
    
    if (!results.sampleData) {
      console.log('\n💡 Note: Consider running the database setup script to add sample data for testing.');
    }
  } else {
    console.log('\n❌ Database verification failed!');
    console.log('Please run the database setup script: npm run db:setup');
    process.exit(1);
  }
}

// Run verification
if (require.main === module) {
  runVerification().catch(err => {
    console.error('❌ Verification failed:', err.message);
    process.exit(1);
  });
}

module.exports = { runVerification };