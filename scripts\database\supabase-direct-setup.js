#!/usr/bin/env node

/**
 * Direct Supabase Database Setup for Doctory Healthcare Platform
 * This script uses Supabase's REST API to create tables directly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🏥 Doctory Database Setup - Direct Supabase Method');
console.log('=================================================\n');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

console.log('✅ Environment variables loaded');
console.log(`URL: ${supabaseUrl}`);
console.log(`Key: ${supabaseKey.substring(0, 20)}...\n`);

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  // Test connection
  console.log('🔍 Testing connection...');
  try {
    const { data, error } = await supabase.auth.getSession();
    console.log('✅ Connected to Supabase\n');
  } catch (err) {
    console.log('⚠️ Connection warning:', err.message);
  }

  console.log('📋 IMPORTANT: Manual Database Setup Required');
  console.log('============================================\n');
  
  console.log('Since Supabase doesn\'t allow direct SQL execution via the client library,');
  console.log('you need to create the tables manually through the Supabase dashboard.\n');
  
  console.log('🚀 STEP-BY-STEP INSTRUCTIONS:');
  console.log('-----------------------------\n');
  
  console.log('1. Open your Supabase project dashboard:');
  console.log(`   ${supabaseUrl.replace('/rest/v1', '')}\n`);
  
  console.log('2. Navigate to: SQL Editor (left sidebar)\n');
  
  console.log('3. Copy and paste this SQL code:\n');
  
  const setupSQL = `-- Doctory Healthcare Platform Database Setup
-- Copy and paste this entire block into Supabase SQL Editor

-- 1. Users table
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('patient', 'doctor', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Patients table
CREATE TABLE IF NOT EXISTS public.patients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Doctors table
CREATE TABLE IF NOT EXISTS public.doctors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  specialty TEXT NOT NULL,
  experience INTEGER NOT NULL DEFAULT 0,
  bio TEXT,
  photo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Availability table
CREATE TABLE IF NOT EXISTS public.availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  doctor_id UUID REFERENCES public.doctors(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
  start_time TEXT NOT NULL,
  end_time TEXT NOT NULL,
  is_recurring BOOLEAN DEFAULT true,
  specific_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Appointments table
CREATE TABLE IF NOT EXISTS public.appointments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  doctor_id UUID REFERENCES public.doctors(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  time TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Medical files table
CREATE TABLE IF NOT EXISTS public.medical_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  appointment_id UUID REFERENCES public.appointments(id) ON DELETE SET NULL,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medical_files ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies (you can customize these later)
-- Users can read their own data
CREATE POLICY "Users can view own data" ON public.users
  FOR SELECT USING (auth.uid()::text = id::text);

-- Patients can manage their own data
CREATE POLICY "Patients can manage own data" ON public.patients
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Doctors can manage their own data
CREATE POLICY "Doctors can manage own data" ON public.doctors
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Everyone can view doctors (for search)
CREATE POLICY "Anyone can view doctors" ON public.doctors
  FOR SELECT USING (true);

-- Doctors can manage their availability
CREATE POLICY "Doctors can manage availability" ON public.availability
  FOR ALL USING (
    doctor_id IN (
      SELECT id FROM public.doctors WHERE user_id::text = auth.uid()::text
    )
  );

-- Everyone can view availability (for booking)
CREATE POLICY "Anyone can view availability" ON public.availability
  FOR SELECT USING (true);

-- Users can manage appointments they're involved in
CREATE POLICY "Users can manage their appointments" ON public.appointments
  FOR ALL USING (
    patient_id IN (
      SELECT id FROM public.patients WHERE user_id::text = auth.uid()::text
    ) OR
    doctor_id IN (
      SELECT id FROM public.doctors WHERE user_id::text = auth.uid()::text
    )
  );

-- Users can manage their medical files
CREATE POLICY "Users can manage their medical files" ON public.medical_files
  FOR ALL USING (
    patient_id IN (
      SELECT id FROM public.patients WHERE user_id::text = auth.uid()::text
    ) OR
    patient_id IN (
      SELECT p.id FROM public.patients p
      JOIN public.appointments a ON p.id = a.patient_id
      JOIN public.doctors d ON a.doctor_id = d.id
      WHERE d.user_id::text = auth.uid()::text
    )
  );

-- Add sample data
INSERT INTO public.doctors (name, specialty, experience, bio) VALUES
('Dr. John Smith', 'Cardiology', 15, 'Experienced cardiologist specializing in heart disease prevention and treatment.'),
('Dr. Ahmed Hassan', 'Pediatrics', 10, 'Dedicated pediatrician with expertise in child healthcare and development.'),
('Dr. Sara Johnson', 'Dermatology', 8, 'Dermatologist focused on skin health and cosmetic procedures.'),
('Dr. Maria Rodriguez', 'Neurology', 12, 'Neurologist specializing in brain and nervous system disorders.'),
('Dr. David Chen', 'Orthopedics', 18, 'Orthopedic surgeon with expertise in joint replacement and sports medicine.')
ON CONFLICT DO NOTHING;

-- Add sample availability for doctors
INSERT INTO public.availability (doctor_id, day_of_week, start_time, end_time, is_recurring) 
SELECT 
  d.id,
  generate_series(1, 5) as day_of_week, -- Monday to Friday
  '09:00' as start_time,
  '17:00' as end_time,
  true as is_recurring
FROM public.doctors d
ON CONFLICT DO NOTHING;`;

  console.log('```sql');
  console.log(setupSQL);
  console.log('```\n');
  
  console.log('4. Click "RUN" to execute the SQL\n');
  
  console.log('5. After successful execution, run this verification:');
  console.log('   node verify-setup.js\n');
  
  console.log('📋 What this SQL creates:');
  console.log('-------------------------');
  console.log('✅ 6 database tables (users, patients, doctors, availability, appointments, medical_files)');
  console.log('✅ Row Level Security (RLS) policies for data protection');
  console.log('✅ 5 sample doctors with different specialties');
  console.log('✅ Sample availability schedules (Mon-Fri, 9AM-5PM)');
  console.log('✅ Proper foreign key relationships');
  console.log('✅ Data validation constraints\n');
  
  console.log('🔍 After setup, verify with:');
  console.log('   node verify-setup.js\n');
  
  console.log('🚀 Then start using your app:');
  console.log('   http://localhost:3001\n');
}

setupDatabase().catch(err => {
  console.error('Setup preparation failed:', err.message);
});