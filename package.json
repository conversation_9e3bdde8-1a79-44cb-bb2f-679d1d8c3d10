{"name": "doctory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test --config=config/development/playwright.config.ts", "test:ui": "playwright test --config=config/development/playwright.config.ts --ui", "test:debug": "playwright test --config=config/development/playwright.config.ts --debug", "db:setup": "node scripts/database/setup-database.js", "db:verify": "node scripts/database/verify-database.js", "db:reset": "node scripts/database/setup-database.js --reset", "verify:env": "node scripts/verification/verify-environment.js", "verify:setup": "node scripts/verification/verify-setup.js"}, "dependencies": {"@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.49.4", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.42.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.3.1", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5"}}