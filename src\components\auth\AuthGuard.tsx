'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading) {
      // Skip auth checks for public pages
      const publicPages = [
        '/',
        '/auth/login',
        '/auth/register',
        '/auth/forgot-password',
        '/auth/reset-password',
        '/api/auth/callback'
      ];

      const isPublicPage = publicPages.some(page => pathname === page);

      if (!user && !isPublicPage) {
        // Not authenticated and trying to access protected page
        router.push(`/auth/login?callbackUrl=${encodeURIComponent(pathname)}`);
        return;
      }

      if (user && user.needsOnboarding && pathname !== '/onboarding') {
        // User needs onboarding but not on onboarding page
        router.push('/onboarding');
        return;
      }

      if (user && !user.needsOnboarding && pathname === '/onboarding') {
        // User doesn't need onboarding but is on onboarding page
        router.push('/dashboard');
        return;
      }

      // If user is authenticated and on auth pages, redirect to dashboard
      if (user && !user.needsOnboarding && pathname.startsWith('/auth/')) {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, loading, pathname, router]);

  // Show loading spinner while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
