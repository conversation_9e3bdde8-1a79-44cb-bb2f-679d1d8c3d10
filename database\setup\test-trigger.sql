-- Test Script for Trigger Function
-- This script tests the handle_new_user() trigger function

-- First, let's check if the trigger function exists
SELECT 
  proname as function_name,
  proargnames as arguments,
  prosrc as function_body
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- Check if the trigger exists
SELECT 
  tgname as trigger_name,
  tgrelid::regclass as table_name,
  tgfoid::regproc as function_name
FROM pg_trigger 
WHERE tgname = 'on_auth_user_created';

-- Check current data in tables
SELECT 'Current users table:' as info;
SELECT id, email, role, created_at FROM public.users ORDER BY created_at DESC LIMIT 5;

SELECT 'Current patients table:' as info;
SELECT id, user_id, name, phone, created_at FROM public.patients ORDER BY created_at DESC LIMIT 5;

SELECT 'Current doctors table:' as info;
SELECT id, user_id, name, specialty, experience, created_at FROM public.doctors ORDE<PERSON> BY created_at DESC LIMIT 5;

-- Test the trigger function manually (for testing purposes only)
-- Note: This simulates what happens when a user signs up through the app

-- Simulate a patient signup
DO $$
DECLARE
  test_user_id UUID := gen_random_uuid();
  test_email TEXT := '<EMAIL>';
BEGIN
  -- This simulates what Supabase Auth does when creating a user
  -- In real usage, this happens automatically when someone signs up
  
  RAISE NOTICE 'Testing trigger with patient role...';
  RAISE NOTICE 'Test User ID: %', test_user_id;
  RAISE NOTICE 'Test Email: %', test_email;
  
  -- Check if this would trigger our function
  -- (We can't actually insert into auth.users from here, but we can test the logic)
  
END $$;

-- Instructions for manual testing
SELECT '
MANUAL TESTING INSTRUCTIONS:
1. Run the fix-trigger-function.sql script first
2. Register a new user through your app with role "patient"
3. Register a new user through your app with role "doctor"  
4. Run this query to verify the results:

SELECT 
  u.id,
  u.email,
  u.role,
  p.name as patient_name,
  d.name as doctor_name,
  d.specialty
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
LEFT JOIN public.doctors d ON u.id = d.user_id
ORDER BY u.created_at DESC;

Expected Results:
- Patient users should have records in both users and patients tables
- Doctor users should have records in both users and doctors tables
- The trigger should create these records automatically
' as instructions;
