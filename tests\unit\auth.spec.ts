import { test, expect } from '@playwright/test';

// Test suite for authentication functionality
test.describe('Authentication', () => {
  // Before each test, navigate to the home page
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });

  // Test for user registration
  test('should register a new patient user', async ({ page }) => {
    // Navigate to register page
    await page.click('text=Sign Up');
    
    // Verify we're on the register page
    await expect(page).toHaveURL(/.*\/auth\/register/);
    
    // Generate a unique email for testing
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'Password123!';
    
    // Fill in registration form
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', testPassword);
    
    // Ensure 'patient' radio button is selected (should be default)
    await expect(page.locator('input[value="patient"]')).toBeChecked();
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if registration was successful (should redirect to profile page)
    await expect(page).toHaveURL(/.*\/profile/, { timeout: 10000 });
  });

  // Test for user registration as doctor
  test('should register a new doctor user', async ({ page }) => {
    // Navigate to register page
    await page.click('text=Sign Up');
    
    // Verify we're on the register page
    await expect(page).toHaveURL(/.*\/auth\/register/);
    
    // Generate a unique email for testing
    const testEmail = `doctor-${Date.now()}@example.com`;
    const testPassword = 'Password123!';
    
    // Fill in registration form
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', testPassword);
    
    // Select 'doctor' radio button
    await page.click('text=Doctors');
    await expect(page.locator('input[value="doctor"]')).toBeChecked();
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if registration was successful (should redirect to profile page)
    await expect(page).toHaveURL(/.*\/profile/, { timeout: 10000 });
  });

  // Test for user login
  test('should login an existing user', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Sign In');
    
    // Verify we're on the login page
    await expect(page).toHaveURL(/.*\/auth\/login/);
    
    // Use a pre-registered test account
    const testEmail = '<EMAIL>';
    const testPassword = 'Password123!';
    
    // Fill in login form
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', testPassword);
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if login was successful (should redirect to profile page)
    await expect(page).toHaveURL(/.*\/profile/, { timeout: 10000 });
  });

  // Test for login with invalid credentials
  test('should show error with invalid login credentials', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Sign In');
    
    // Fill in login form with invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check if error message is displayed
    await expect(page.locator('.bg-red-100')).toBeVisible();
    await expect(page.locator('.bg-red-100')).toContainText(/error|invalid/i);
  });

  // Test for registration validation
  test('should validate registration form', async ({ page }) => {
    // Navigate to register page
    await page.click('text=Sign Up');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Check if form validation prevents submission (we should still be on register page)
    await expect(page).toHaveURL(/.*\/auth\/register/);
  });

  // Test for navigation between login and register pages
  test('should navigate between login and register pages', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Sign In');
    
    // Click on the sign up link
    await page.click('text=Sign Up', { exact: true });
    
    // Verify we're on the register page
    await expect(page).toHaveURL(/.*\/auth\/register/);
    
    // Click on the sign in link
    await page.click('text=Sign In', { exact: true });
    
    // Verify we're back on the login page
    await expect(page).toHaveURL(/.*\/auth\/login/);
  });
});