'use client';

import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';

type StatusBadgeProps = {
  status: 'pending' | 'confirmed' | 'cancelled';
  className?: string;
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  const { t } = useLanguage();
  
  const statusStyles = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    confirmed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status]} ${className}`}>
      {t(status)}
    </span>
  );
};
