# Manual Database Verification Checklist

## Overview
This checklist helps you manually verify that your Doctory database is properly set up and ready for development.

## Prerequisites ✅
- [ ] Supabase project created
- [ ] Environment variables set in `.env.local`
- [ ] Database setup script has been run

## Environment Variables Check

### 1. Check `.env.local` file exists
```bash
ls -la .env.local
```

### 2. Verify environment variables are set
Your `.env.local` should contain:
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

**Current Status:**
- ✅ SUPABASE_URL: `https://izsqflrheknprmvvadga.supabase.co`
- ✅ SUPABASE_ANON_KEY: Set (starts with `eyJhbGciOiJIUzI1NiIs...`)

## Database Tables Verification

### 1. Check Tables Exist in Supabase Dashboard
Go to your Supabase project → Table Editor and verify these tables exist:

- [ ] `users` - User accounts table
- [ ] `patients` - Patient profiles table  
- [ ] `doctors` - Doctor profiles table
- [ ] `availability` - Doctor availability schedules
- [ ] `appointments` - Appointment bookings
- [ ] `medical_files` - Medical file references

### 2. Check Table Structure
Each table should have the correct columns:

#### `users` table:
- [ ] `id` (uuid, primary key)
- [ ] `email` (text, unique)
- [ ] `role` (text, check constraint: patient/doctor/admin)
- [ ] `created_at` (timestamp)

#### `patients` table:
- [ ] `id` (uuid, primary key)
- [ ] `user_id` (uuid, foreign key to users)
- [ ] `name` (text)
- [ ] `phone` (text, nullable)
- [ ] `created_at` (timestamp)

#### `doctors` table:
- [ ] `id` (uuid, primary key)
- [ ] `user_id` (uuid, foreign key to users)
- [ ] `name` (text)
- [ ] `specialty` (text)
- [ ] `experience` (integer)
- [ ] `bio` (text, nullable)
- [ ] `photo_url` (text, nullable)
- [ ] `created_at` (timestamp)

#### `availability` table:
- [ ] `id` (uuid, primary key)
- [ ] `doctor_id` (uuid, foreign key to doctors)
- [ ] `day_of_week` (integer, 0-6)
- [ ] `start_time` (text)
- [ ] `end_time` (text)
- [ ] `is_recurring` (boolean)
- [ ] `specific_date` (date, nullable)
- [ ] `created_at` (timestamp)

#### `appointments` table:
- [ ] `id` (uuid, primary key)
- [ ] `patient_id` (uuid, foreign key to patients)
- [ ] `doctor_id` (uuid, foreign key to doctors)
- [ ] `date` (date)
- [ ] `time` (text)
- [ ] `status` (text, check constraint: pending/confirmed/cancelled)
- [ ] `notes` (text, nullable)
- [ ] `created_at` (timestamp)

#### `medical_files` table:
- [ ] `id` (uuid, primary key)
- [ ] `patient_id` (uuid, foreign key to patients)
- [ ] `appointment_id` (uuid, foreign key to appointments, nullable)
- [ ] `file_name` (text)
- [ ] `file_url` (text)
- [ ] `file_type` (text)
- [ ] `created_at` (timestamp)

## Row Level Security (RLS) Verification

### 1. Check RLS is Enabled
In Supabase Dashboard → Authentication → Policies, verify RLS is enabled for:
- [ ] `users` table
- [ ] `patients` table
- [ ] `doctors` table
- [ ] `availability` table
- [ ] `appointments` table
- [ ] `medical_files` table

### 2. Check Policies Exist
Each table should have appropriate policies for:
- [ ] SELECT (read) operations
- [ ] INSERT (create) operations  
- [ ] UPDATE (modify) operations
- [ ] DELETE (remove) operations (where applicable)

## Storage Verification

### 1. Check Storage Bucket
In Supabase Dashboard → Storage:
- [ ] `medical_files` bucket exists
- [ ] Bucket is private (not public)
- [ ] File size limit is set (50MB)
- [ ] Allowed MIME types are configured

### 2. Check Storage Policies
In Storage → Policies:
- [ ] Patients can upload their own files
- [ ] Patients can view their own files
- [ ] Doctors can view patient files (for their patients)
- [ ] Patients can delete their own files
- [ ] Patients can update their own files

## Sample Data Verification

### 1. Check Sample Doctors
In Table Editor → `doctors` table:
- [ ] At least 3 sample doctors exist
- [ ] Each doctor has: name, specialty, experience, bio
- [ ] Specialties include: Cardiology, Pediatrics, Dermatology

### 2. Check Sample Availability
In Table Editor → `availability` table:
- [ ] Sample availability records exist for doctors
- [ ] Different days and times are covered
- [ ] Both recurring and specific date entries exist

## Authentication Verification

### 1. Check Auth Configuration
In Supabase Dashboard → Authentication → Settings:
- [ ] Email authentication is enabled
- [ ] User creation is allowed
- [ ] Email confirmation is configured (optional for development)

### 2. Check Trigger Function
In SQL Editor, verify the trigger function exists:
```sql
SELECT * FROM pg_proc WHERE proname = 'handle_new_user';
```
- [ ] `handle_new_user` function exists
- [ ] Trigger is attached to `auth.users` table

## Manual Testing Commands

### Run Verification Script
```bash
# Quick verification
node scripts/simple-verify.js

# Full verification  
npm run db:verify
```

### Test Database Connection
```bash
# Test with curl (if you have API endpoints)
curl -X GET "https://izsqflrheknprmvvadga.supabase.co/rest/v1/doctors" \
  -H "apikey: your-anon-key" \
  -H "Authorization: Bearer your-anon-key"
```

## Troubleshooting

### Common Issues:
1. **Tables don't exist**: Run database setup script
2. **RLS blocking queries**: Check policies are correctly configured
3. **Storage bucket missing**: Create manually or run storage setup
4. **Sample data missing**: Run setup script with sample data flag

### Quick Fixes:
```bash
# Re-run database setup
npm run db:setup

# Reset and setup fresh
npm run db:reset
```

## Success Criteria ✅

Your database is ready when:
- [ ] All tables exist with correct structure
- [ ] RLS is enabled with appropriate policies
- [ ] Storage bucket is configured with policies
- [ ] Sample data is present for testing
- [ ] Authentication system is working
- [ ] Verification script passes all tests

## Next Steps

Once verification is complete:
1. [ ] Test user registration and login
2. [ ] Test doctor search functionality
3. [ ] Test appointment booking flow
4. [ ] Test file upload/download
5. [ ] Run application in development mode

---

**Last Updated:** January 2025  
**Version:** 1.0  
**Project:** Doctory Healthcare Platform