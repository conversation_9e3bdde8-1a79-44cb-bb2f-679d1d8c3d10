# Comprehensive Appointment Booking System Implementation Plan

## Part 1: Trigger Fix Summary

### Issue Resolved
- **Problem**: Users were created in `users` table but corresponding `patients`/`doctors` records were not created
- **Root Cause**: Trigger function was hardcoded to create only 'patient' role and didn't create profile records
- **Solution**: Updated trigger to read role from user metadata and create appropriate profile records

### Files Created/Modified
1. **`database/setup/fix-trigger-function.sql`** - Fixed trigger function
2. **`src/providers/AuthProvider.tsx`** - Pass role in user metadata
3. **`src/lib/auth.tsx`** - Simplified to rely on trigger
4. **`database/setup/test-trigger.sql`** - Testing script

### Testing Steps
1. Run `fix-trigger-function.sql` in Supabase SQL Editor
2. Register new users with different roles
3. Verify automatic profile creation in respective tables

---

## Part 2: Appointment Booking System Architecture

### 1. Doctor Availability Management

#### 1.1 Database Schema Enhancement
```sql
-- Enhanced availability table
ALTER TABLE public.availability ADD COLUMN IF NOT EXISTS:
  - recurring_type TEXT CHECK (recurring_type IN ('weekly', 'specific_date'))
  - day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6) -- 0=Sunday
  - specific_date DATE
  - is_blocked BOOLEAN DEFAULT FALSE
  - max_appointments INTEGER DEFAULT 1
```

#### 1.2 API Endpoints Required
- `POST /api/doctors/availability` - Create availability slot
- `GET /api/doctors/{id}/availability` - Get doctor's availability
- `PUT /api/doctors/availability/{id}` - Update availability
- `DELETE /api/doctors/availability/{id}` - Remove availability
- `POST /api/doctors/availability/bulk` - Bulk create weekly schedule

#### 1.3 Frontend Components
```
src/components/doctors/
├── AvailabilityManager.tsx       # Main availability management
├── WeeklySchedule.tsx           # Weekly recurring schedule
├── SpecificDateSchedule.tsx     # One-time date availability
├── AvailabilityCalendar.tsx     # Calendar view for doctors
└── TimeSlotPicker.tsx           # Time slot selection component
```

### 2. Patient Appointment Booking Flow

#### 2.1 Doctor Discovery & Selection
```
src/components/booking/
├── DoctorSearch.tsx             # Search doctors by specialty/name
├── DoctorGrid.tsx               # Grid view of available doctors
├── DoctorProfile.tsx            # Detailed doctor information
└── SpecialtyFilter.tsx          # Filter by medical specialty
```

#### 2.2 Appointment Booking Process
```
src/components/booking/
├── BookingWizard.tsx            # Multi-step booking process
├── AvailabilityViewer.tsx       # Show available time slots
├── AppointmentForm.tsx          # Booking form with details
├── BookingConfirmation.tsx      # Confirmation step
└── PaymentIntegration.tsx       # Payment processing (future)
```

#### 2.3 Booking Flow Steps
1. **Doctor Selection** - Browse/search doctors
2. **Date Selection** - Choose preferred date
3. **Time Selection** - Pick available time slot
4. **Details Entry** - Reason for visit, notes
5. **Confirmation** - Review and confirm booking
6. **Notification** - Email/SMS confirmation

### 3. Appointment Management System

#### 3.1 Dashboard Components
```
src/components/appointments/
├── AppointmentDashboard.tsx     # Main dashboard for both roles
├── UpcomingAppointments.tsx     # Next appointments widget
├── AppointmentHistory.tsx       # Past appointments
├── AppointmentDetails.tsx       # Detailed appointment view
└── AppointmentActions.tsx       # Cancel/reschedule actions
```

#### 3.2 Status Management
```typescript
type AppointmentStatus = 
  | 'pending'      // Awaiting doctor confirmation
  | 'confirmed'    // Doctor confirmed
  | 'in_progress'  // Currently happening
  | 'completed'    // Finished successfully
  | 'cancelled'    // Cancelled by patient/doctor
  | 'no_show'      // Patient didn't show up
```

#### 3.3 API Endpoints
- `GET /api/appointments/patient/{id}` - Patient's appointments
- `GET /api/appointments/doctor/{id}` - Doctor's appointments
- `POST /api/appointments` - Create new appointment
- `PUT /api/appointments/{id}/status` - Update appointment status
- `PUT /api/appointments/{id}/reschedule` - Reschedule appointment
- `DELETE /api/appointments/{id}` - Cancel appointment

### 4. Real-time Availability System

#### 4.1 Conflict Prevention
```typescript
// Availability checking logic
interface AvailabilityCheck {
  doctorId: string;
  date: string;
  startTime: string;
  endTime: string;
}

// Database query to prevent double booking
const checkAvailability = async (check: AvailabilityCheck) => {
  // 1. Check if doctor has availability slot
  // 2. Check if slot is not already booked
  // 3. Check if doctor hasn't blocked the time
  // 4. Return available time slots
}
```

#### 4.2 Real-time Updates
```
src/hooks/
├── useRealTimeAvailability.ts   # Real-time availability updates
├── useAppointmentUpdates.ts     # Live appointment status updates
└── useNotifications.ts          # Real-time notifications
```

### 5. Notification System

#### 5.1 Email Templates
```
src/lib/email/
├── templates/
│   ├── appointment-confirmation.tsx
│   ├── appointment-reminder.tsx
│   ├── appointment-cancelled.tsx
│   └── appointment-rescheduled.tsx
└── emailService.ts
```

#### 5.2 Notification Triggers
- **Booking Confirmation** - Immediate after booking
- **24h Reminder** - Day before appointment
- **2h Reminder** - 2 hours before appointment
- **Status Changes** - Cancellation, rescheduling
- **Doctor Responses** - Confirmation/rejection

### 6. Implementation Priority & Timeline

#### Phase 1: Core Booking (Week 1-2)
1. **Fix trigger function** ✅
2. **Enhanced availability table**
3. **Basic doctor availability management**
4. **Simple appointment booking flow**
5. **Basic appointment listing**

#### Phase 2: Advanced Features (Week 3-4)
1. **Real-time availability checking**
2. **Appointment status management**
3. **Cancellation and rescheduling**
4. **Email notifications**
5. **Doctor dashboard enhancements**

#### Phase 3: User Experience (Week 5-6)
1. **Advanced search and filtering**
2. **Calendar views**
3. **Mobile responsiveness**
4. **Performance optimizations**
5. **Testing and bug fixes**

### 7. Technical Implementation Details

#### 7.1 Database Queries for Availability
```sql
-- Get available time slots for a doctor on a specific date
WITH doctor_availability AS (
  SELECT start_time, end_time, max_appointments
  FROM availability 
  WHERE doctor_id = $1 
    AND (
      (recurring_type = 'weekly' AND day_of_week = EXTRACT(DOW FROM $2))
      OR (recurring_type = 'specific_date' AND specific_date = $2)
    )
    AND is_blocked = FALSE
),
booked_slots AS (
  SELECT start_time, COUNT(*) as booked_count
  FROM appointments 
  WHERE doctor_id = $1 
    AND date = $2 
    AND status NOT IN ('cancelled', 'no_show')
  GROUP BY start_time
)
SELECT da.start_time, da.end_time, 
       (da.max_appointments - COALESCE(bs.booked_count, 0)) as available_slots
FROM doctor_availability da
LEFT JOIN booked_slots bs ON da.start_time = bs.start_time
WHERE (da.max_appointments - COALESCE(bs.booked_count, 0)) > 0
ORDER BY da.start_time;
```

#### 7.2 State Management
```typescript
// Booking state management
interface BookingState {
  selectedDoctor: Doctor | null;
  selectedDate: Date | null;
  selectedTime: string | null;
  appointmentDetails: {
    reason: string;
    notes: string;
  };
  step: 'doctor' | 'date' | 'time' | 'details' | 'confirmation';
}
```

### 8. File Structure Overview
```
src/
├── components/
│   ├── booking/           # Appointment booking components
│   ├── doctors/           # Doctor management components
│   ├── appointments/      # Appointment management
│   └── notifications/     # Notification components
├── hooks/
│   ├── useBooking.ts      # Booking state management
│   ├── useAvailability.ts # Availability checking
│   └── useAppointments.ts # Appointment management
├── lib/
│   ├── api/
│   │   ├── appointments.ts
│   │   ├── availability.ts
│   │   └── notifications.ts
│   └── email/             # Email service
├── pages/
│   ├── booking/           # Booking flow pages
│   ├── appointments/      # Appointment management pages
│   └── doctors/           # Doctor dashboard pages
└── types/
    ├── appointment.ts     # Appointment types
    ├── availability.ts    # Availability types
    └── booking.ts         # Booking types
```

This comprehensive plan provides a complete roadmap for implementing a robust appointment booking system with real-time availability, conflict prevention, and excellent user experience.
