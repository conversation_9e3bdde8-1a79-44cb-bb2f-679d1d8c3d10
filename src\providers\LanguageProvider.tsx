/**
 * Language Provider for the Doctory application
 * Manages language state and provides translation functionality
 */

'use client';

import React, { createContext, useEffect, useState } from 'react';
import { Language, LANGUAGES, DEFAULT_LANGUAGE, LANGUAGE_STORAGE_KEY, RTL_LANGUAGES } from '@/constants/languages';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
  isRTL: boolean;
  t: (key: string, params?: Record<string, string>) => string;
}

export const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: React.ReactNode;
}

// Simple translation function (can be enhanced with proper i18n library)
const translations: Record<Language, Record<string, string>> = {
  [LANGUAGES.EN]: {
    // Navigation
    'nav.home': 'Home',
    'nav.dashboard': 'Dashboard',
    'nav.appointments': 'Appointments',
    'nav.doctors': 'Doctors',
    'nav.patients': 'Patients',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.logout': 'Logout',
    
    // Authentication
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.logout': 'Logout',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.forgotPassword': 'Forgot Password',
    'auth.resetPassword': 'Reset Password',
    'auth.signIn': 'Sign In',
    'auth.signUp': 'Sign Up',
    
    // Common
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',

    // Dashboard
    'dashboard': 'Dashboard',
    'loading': 'Loading...',
    'appointments': 'Appointments',
    'noAppointments': 'No appointments found',


    'common.language': 'Language',
    'common.english': 'English',
    'common.arabic': 'Arabic',


    // Doctor search
    'doctors.searchPlaceholder': 'Search doctors by name',
    'doctors.allSpecialties': 'All Specialties',
    'doctors.years': 'years',
    'doctors.experience': 'experience',
    'doctors.bookAppointment': 'Book Appointment',
    
    // Medical Summary
    'medical.summary': 'Medical Summary',
    'medical.files': 'Medical Files',
    'medical.recentFiles': 'Recent files uploaded',
    'medical.completedAppointments': 'Completed Appointments',
    'medical.pastVisits': 'Past visits with doctors',
    'medical.viewFiles': 'View Medical Files',
    
    // File Upload
    'files.uploadMedicalFiles': 'Upload Medical Files',
    'files.selectFile': 'Select File',
    'files.acceptedFileTypes': 'Accepted file types',
    'files.pleaseSelectFile': 'Please select a file',
    'files.fileUploadError': 'Error uploading file',
    'files.upload': 'Upload',
    
    // Status
    'pending': 'Pending',
    'confirmed': 'Confirmed',
    'cancelled': 'Cancelled',
  },
  [LANGUAGES.AR]: {
    // Navigation
    'nav.home': 'الصفحة الرئيسية',
    'nav.dashboard': 'لوحة التحكم',
    'nav.appointments': 'المواعيد',
    'nav.doctors': 'الأطباء',
    'nav.patients': 'المرضى',
    'nav.profile': 'الملف الشخصي',
    'nav.settings': 'الإعدادات',
    'nav.logout': 'تسجيل الخروج',
    
    // Authentication
    'auth.login': 'تسجيل الدخول',
    'auth.register': 'إنشاء حساب',
    'auth.logout': 'تسجيل الخروج',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.confirmPassword': 'تأكيد كلمة المرور',
    'auth.forgotPassword': 'نسيت كلمة المرور',
    'auth.resetPassword': 'إعادة تعيين كلمة المرور',
    'auth.signIn': 'دخول',
    'auth.signUp': 'إنشاء حساب',
    
    // Common
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء',
    'common.delete': 'حذف',
    'common.edit': 'تعديل',
    'common.view': 'عرض',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.confirm': 'تأكيد',
    'common.yes': 'نعم',
    'common.no': 'لا',

    // Dashboard
    'dashboard': 'لوحة التحكم',
    'loading': 'جاري التحميل...',
    'appointments': 'المواعيد',
    'noAppointments': 'لا توجد مواعيد',
    
    // Language
    'common.language': 'اللغة',
    'common.english': 'الإنجليزية',
    'common.arabic': 'العربية',
    
    // Doctor search
    'doctors.searchPlaceholder': 'البحث عن الأطباء بالاسم',
    'doctors.allSpecialties': 'جميع التخصصات',
    'doctors.years': 'سنوات',
    'doctors.experience': 'خبرة',
    'doctors.bookAppointment': 'حجز موعد',
    
    // Medical Summary
    'medical.summary': 'ملخص طبي',
    'medical.files': 'ملفات طبية',
    'medical.recentFiles': 'الملفات المضافة مؤخرًا',
    'medical.completedAppointments': 'المواعيد المكتملة',
    'medical.pastVisits': 'زيارات سابقة مع الأطباء',
    'medical.viewFiles': 'عرض ملفات الطبي',
    
    // File Upload
    'files.uploadMedicalFiles': 'تحميل الملفات الطبية',
    'files.selectFile': 'اختر ملف',
    'files.acceptedFileTypes': 'أنواع الملفات المقبولة',
    'files.pleaseSelectFile': 'يرجى اختيار ملف',
    'files.fileUploadError': 'خطأ في تحميل الملف',
    'files.upload': 'تحميل',
    
    // Status
    'pending': 'قيد الانتظار',
    'confirmed': 'مؤكد',
    'cancelled': 'ملغي',
  },
};

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>(DEFAULT_LANGUAGE);
  const [mounted, setMounted] = useState(false);

  const toggleLanguage = () => {
    const languages = Object.values(LANGUAGES);
    const currentIndex = languages.indexOf(language!);
    const nextIndex = (currentIndex + 1) % languages.length;
    setLanguage(languages[nextIndex]);
  };

  const isRTL = RTL_LANGUAGES.includes(language!);

  const t = (key: string, params?: Record<string, string>): string => {
    let translation = translations[language!]?.[key] || key;
    
    // Simple parameter replacement
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, value);
      });
    }
    
    return translation;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    toggleLanguage,
    isRTL,
    t,
  };

  useEffect(() => {
    if (!mounted) return;

    // Apply language direction to document
    const root = document.documentElement;
    const isRTL = RTL_LANGUAGES.includes(language!);
    
    root.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
    root.setAttribute('lang', language!);
    
    // Save to localStorage
    localStorage.setItem(LANGUAGE_STORAGE_KEY, language!);
  }, [language, mounted]);

  useEffect(() => {
    setMounted(true);

    // Load language from localStorage after mounting
    if (typeof window !== 'undefined') {
      const storedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY) as Language | null;
      if (storedLanguage && Object.values(LANGUAGES).includes(storedLanguage)) {
        setLanguage(storedLanguage);
      }
    }
  }, []);

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return <div style={{ visibility: 'hidden' }}>{children}</div>;
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

