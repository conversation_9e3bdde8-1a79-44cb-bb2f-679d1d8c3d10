import { test, expect } from '@playwright/test';

/**
 * Database Schema Tests for Doctory Healthcare Platform
 * 
 * These tests verify that the database schema is properly set up
 * and all tables, relationships, and policies are working correctly.
 */

test.describe('Database Schema Verification', () => {
  
  test('should have all required tables accessible', async ({ page }) => {
    // Navigate to the app to trigger any initial database connections
    await page.goto('/');
    
    // Test if we can access the database through the API
    const response = await page.evaluate(async () => {
      try {
        // Import the createClient function
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        // Test each table
        const tables = ['users', 'patients', 'doctors', 'availability', 'appointments', 'medical_files'];
        const results = {};
        
        for (const table of tables) {
          try {
            const { data, error } = await supabase.from(table).select('*').limit(1);
            results[table] = error ? `Error: ${error.message}` : 'Success';
          } catch (err) {
            results[table] = `Exception: ${err.message}`;
          }
        }
        
        return results;
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify all tables are accessible
    expect(response.users).toBe('Success');
    expect(response.patients).toBe('Success');
    expect(response.doctors).toBe('Success');
    expect(response.availability).toBe('Success');
    expect(response.appointments).toBe('Success');
    expect(response.medical_files).toBe('Success');
  });

  test('should have sample doctors data', async ({ page }) => {
    await page.goto('/');
    
    const doctorsData = await page.evaluate(async () => {
      try {
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        const { data: doctors, error } = await supabase
          .from('doctors')
          .select('name, specialty, experience');
        
        if (error) {
          return { error: error.message };
        }
        
        return { doctors, count: doctors?.length || 0 };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify sample doctors exist
    expect(doctorsData.count).toBeGreaterThan(0);
    expect(doctorsData.doctors).toBeDefined();
    
    // Check for specific sample doctors
    const doctorNames = doctorsData.doctors?.map(d => d.name) || [];
    expect(doctorNames).toContain('Dr. John Smith');
    expect(doctorNames).toContain('Dr. Ahmed Hassan');
    expect(doctorNames).toContain('Dr. Sara Johnson');
  });

  test('should have proper doctor availability data', async ({ page }) => {
    await page.goto('/');
    
    const availabilityData = await page.evaluate(async () => {
      try {
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        const { data: availability, error } = await supabase
          .from('availability')
          .select('doctor_id, day_of_week, start_time, end_time, is_recurring');
        
        if (error) {
          return { error: error.message };
        }
        
        return { availability, count: availability?.length || 0 };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify availability data exists
    expect(availabilityData.count).toBeGreaterThan(0);
    expect(availabilityData.availability).toBeDefined();
    
    // Verify data structure
    const firstAvailability = availabilityData.availability?.[0];
    if (firstAvailability) {
      expect(firstAvailability.doctor_id).toBeDefined();
      expect(firstAvailability.day_of_week).toBeGreaterThanOrEqual(0);
      expect(firstAvailability.day_of_week).toBeLessThanOrEqual(6);
      expect(firstAvailability.start_time).toBeDefined();
      expect(firstAvailability.end_time).toBeDefined();
      expect(typeof firstAvailability.is_recurring).toBe('boolean');
    }
  });

  test('should have storage bucket configured', async ({ page }) => {
    await page.goto('/');
    
    const storageData = await page.evaluate(async () => {
      try {
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        const { data: buckets, error } = await supabase.storage.listBuckets();
        
        if (error) {
          return { error: error.message };
        }
        
        const medicalFilesBucket = buckets.find(bucket => bucket.id === 'medical_files');
        
        return { 
          buckets: buckets.map(b => ({ id: b.id, name: b.name, public: b.public })),
          medicalFilesBucket: medicalFilesBucket ? {
            id: medicalFilesBucket.id,
            name: medicalFilesBucket.name,
            public: medicalFilesBucket.public
          } : null
        };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify medical files bucket exists
    expect(storageData.medicalFilesBucket).toBeDefined();
    expect(storageData.medicalFilesBucket?.id).toBe('medical_files');
    expect(storageData.medicalFilesBucket?.public).toBe(false); // Should be private
  });

  test('should support doctor search functionality', async ({ page }) => {
    await page.goto('/');
    
    const searchResults = await page.evaluate(async () => {
      try {
        const { searchDoctors } = await import('../src/lib/api');
        
        // Test search by name
        const nameResults = await searchDoctors('Smith');
        
        // Test search by specialty
        const specialtyResults = await searchDoctors('', 'Cardiology');
        
        return {
          nameResults: nameResults?.length || 0,
          specialtyResults: specialtyResults?.length || 0,
          nameData: nameResults,
          specialtyData: specialtyResults
        };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify search functionality works
    expect(searchResults.nameResults).toBeGreaterThan(0);
    expect(searchResults.specialtyResults).toBeGreaterThan(0);
    
    // Verify search results contain expected data
    const smithDoctor = searchResults.nameData?.find(d => d.name.includes('Smith'));
    expect(smithDoctor).toBeDefined();
    expect(smithDoctor?.specialty).toBe('Cardiology');
    
    const cardiologist = searchResults.specialtyData?.find(d => d.specialty === 'Cardiology');
    expect(cardiologist).toBeDefined();
  });

  test('should have proper table relationships', async ({ page }) => {
    await page.goto('/');
    
    const relationshipData = await page.evaluate(async () => {
      try {
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        // Test doctor-availability relationship
        const { data: doctorsWithAvailability, error: docError } = await supabase
          .from('doctors')
          .select(`
            id,
            name,
            availability (
              day_of_week,
              start_time,
              end_time
            )
          `)
          .limit(1);
        
        if (docError) {
          return { error: docError.message };
        }
        
        return {
          doctorsWithAvailability,
          hasRelationship: doctorsWithAvailability?.[0]?.availability?.length > 0
        };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Verify relationships work
    expect(relationshipData.doctorsWithAvailability).toBeDefined();
    expect(relationshipData.hasRelationship).toBe(true);
  });

});

test.describe('Database Security', () => {
  
  test('should have Row Level Security enabled', async ({ page }) => {
    await page.goto('/');
    
    const rlsStatus = await page.evaluate(async () => {
      try {
        const { createClient } = await import('../src/lib/supabase');
        const supabase = createClient();
        
        // Try to access data without authentication (should work for public data like doctors)
        const { data: doctors, error } = await supabase
          .from('doctors')
          .select('name, specialty')
          .limit(1);
        
        return {
          canAccessDoctors: !error,
          doctorCount: doctors?.length || 0,
          error: error?.message
        };
      } catch (err) {
        return { error: err.message };
      }
    });
    
    // Should be able to access public doctor data
    expect(rlsStatus.canAccessDoctors).toBe(true);
    expect(rlsStatus.doctorCount).toBeGreaterThan(0);
  });

});