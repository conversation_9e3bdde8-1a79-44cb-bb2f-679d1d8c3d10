'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { OnboardingForm } from '@/components/onboarding/OnboardingForm';

export default function OnboardingPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [userRole, setUserRole] = useState<'patient' | 'doctor' | null>(null);

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Not authenticated, redirect to login
        router.push('/auth/login');
        return;
      }

      if (!user.needsOnboarding) {
        // Profile already complete, redirect to dashboard
        router.push('/dashboard');
        return;
      }

      if (user.role) {
        setUserRole(user.role as 'patient' | 'doctor');
      } else {
        // No role assigned, something went wrong
        console.error('User has no role assigned');
        router.push('/auth/login');
      }
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!userRole) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <p className="text-muted-foreground">Redirecting...</p>
        </div>
      </div>
    );
  }

  return <OnboardingForm userRole={userRole} />;
}
