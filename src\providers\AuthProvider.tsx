/**
 * Authentication Provider for the Doctory application
 * Manages user authentication state and provides auth methods
 */

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/lib/supabase';

const supabase = createClient();

interface AuthUser extends User {
  role?: 'patient' | 'doctor' | 'admin';
  profileComplete?: boolean;
  needsOnboarding?: boolean;
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, role: 'patient' | 'doctor') => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  refreshUser: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial user (secure method)
    const getInitialUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (user && !error) {
        const userWithRole = await getUserWithRole(user);
        setUser(userWithRole);
      }
      setLoading(false);
    };

    getInitialUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (session?.user) {
          const userWithRole = await getUserWithRole(session.user);
          setUser(userWithRole);
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const getUserWithRole = async (user: User): Promise<AuthUser> => {
    try {
      // First, try to get user profile from public.users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role, created_at')
        .eq('id', user.id)
        .single();

      if (userError) {
        console.error('Error fetching user profile:', userError);
        // If user profile doesn't exist, they need onboarding
        return {
          ...user,
          role: undefined,
          needsOnboarding: true,
          profileComplete: false
        };
      }

      // Check if user has completed their profile based on role
      let profileComplete = false;
      let needsOnboarding = false;

      if (userData.role === 'patient') {
        const { data: patientData, error: patientError } = await supabase
          .from('patients')
          .select('name, phone')
          .eq('user_id', user.id)
          .single();

        if (patientError || !patientData) {
          needsOnboarding = true;
          profileComplete = false;
        } else {
          // Check if patient has completed basic profile
          profileComplete = !!(patientData.name && patientData.name !== user.email?.split('@')[0]);
          needsOnboarding = !profileComplete;
        }
      } else if (userData.role === 'doctor') {
        const { data: doctorData, error: doctorError } = await supabase
          .from('doctors')
          .select('name, specialty')
          .eq('user_id', user.id)
          .single();

        if (doctorError || !doctorData) {
          needsOnboarding = true;
          profileComplete = false;
        } else {
          // Check if doctor has completed basic profile
          profileComplete = !!(doctorData.name && doctorData.specialty &&
                             doctorData.name !== user.email?.split('@')[0]);
          needsOnboarding = !profileComplete;
        }
      }

      return {
        ...user,
        role: userData.role as 'patient' | 'doctor' | 'admin',
        profileComplete,
        needsOnboarding
      };
    } catch (error) {
      console.error('Error fetching user role:', error);
      // On any error, assume they need onboarding
      return {
        ...user,
        role: undefined,
        needsOnboarding: true,
        profileComplete: false
      };
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, role: 'patient' | 'doctor') => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          role,
          name: email.split('@')[0], // Default name from email
        },
        emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : ''}/api/auth/callback`,
      },
    });
    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${typeof window !== 'undefined' ? window.location.origin : ''}/api/auth/callback?next=/auth/reset-password`,
    });
    return { error };
  };

  const refreshUser = async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (user && !error) {
      const userWithRole = await getUserWithRole(user);
      setUser(userWithRole);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};