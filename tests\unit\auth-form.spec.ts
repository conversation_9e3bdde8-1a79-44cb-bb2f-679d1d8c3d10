import { test, expect } from '@playwright/test';

test('Auth form input text is visible', async ({ page }) => {
  // Navigate to the register page
  await page.goto('/auth/register');
  
  // Check that the page title is visible
  await expect(page.getByText('Sign Up')).toBeVisible();
  
  // Type in the email field and verify it's visible
  await page.getByLabel('Email').fill('<EMAIL>');
  await expect(page.getByLabel('Email')).toHaveValue('<EMAIL>');
  
  // Type in the password field and verify it's visible (though the actual text is masked)
  await page.getByLabel('Password').fill('password123');
  await expect(page.getByLabel('Password')).toHaveValue('password123');
  
  // Check that the radio buttons are visible and can be selected
  await expect(page.getByText('Patients')).toBeVisible();
  await expect(page.getByText('Doctors')).toBeVisible();
  
  // Select the doctor role
  await page.getByLabel('Doctors').check();
  await expect(page.getByLabel('Doctors')).toBeChecked();
});