# Doctory Database Setup Guide

This guide will help you set up the complete database schema for the Doctory Healthcare Platform.

## Prerequisites

1. **Supabase Project**: Ensure you have a Supabase project created
2. **Environment Variables**: Verify your `.env.local` file contains:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

## Setup Methods

### Method 1: Automated Setup (Recommended)

1. **Install dependencies** (if not already done):
   ```bash
   npm install @supabase/supabase-js dotenv
   ```

2. **Run the setup script**:
   ```bash
   node scripts/setup-database.js
   ```

### Method 2: Manual Setup via Supabase Dashboard

If the automated script doesn't work, follow these manual steps:

#### Step 1: Create Tables

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `supabase/migrations/20240102000000_complete_schema.sql`
4. Click **Run** to execute the migration

#### Step 2: Setup Storage

1. In the same SQL Editor, copy and paste the contents of `supabase/migrations/20240102000001_storage_setup.sql`
2. Click **Run** to execute the storage setup

#### Step 3: Verify Setup

1. Go to **Table Editor** and verify these tables exist:
   - `users`
   - `patients`
   - `doctors`
   - `availability`
   - `appointments`
   - `medical_files`

2. Go to **Storage** and verify the `medical_files` bucket exists

## Database Schema Overview

### Tables Created

1. **users** - Extends auth.users with role information
2. **patients** - Patient-specific information
3. **doctors** - Doctor profiles and specialties
4. **availability** - Doctor availability schedules
5. **appointments** - Appointment bookings
6. **medical_files** - Medical file metadata

### Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Role-based access control** (patient, doctor, admin)
- **Secure file storage** with proper access policies
- **Automatic user creation** trigger for new registrations

### Sample Data

The migration includes sample doctors and availability schedules for testing:

- **Dr. John Smith** (Cardiology) - Available Mon, Tue, Wed
- **Dr. Ahmed Hassan** (Pediatrics) - Available Mon, Wed, Fri  
- **Dr. Sara Johnson** (Dermatology) - Available Tue, Thu, Sat

## Testing the Setup

### 1. Test User Registration

```javascript
// Test patient registration
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123'
});
```

### 2. Test Doctor Data Retrieval

```javascript
// Test fetching doctors
const { data: doctors, error } = await supabase
  .from('doctors')
  .select('*');
```

### 3. Test Appointment Creation

```javascript
// Test creating an appointment (after user is logged in)
const { data, error } = await supabase
  .from('appointments')
  .insert({
    patient_id: 'patient_uuid',
    doctor_id: 'doctor_uuid',
    date: '2024-01-15',
    time: '10:00',
    status: 'pending'
  });
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure you're using the service role key for setup
2. **Table Already Exists**: The migrations use `IF NOT EXISTS` to prevent conflicts
3. **RLS Policy Conflicts**: The script drops existing policies before creating new ones

### Verification Queries

Run these in the SQL Editor to verify setup:

```sql
-- Check if all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'patients', 'doctors', 'availability', 'appointments', 'medical_files');

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;

-- Check sample data
SELECT name, specialty FROM doctors;
```

## Next Steps

After successful database setup:

1. **Test Authentication**: Verify user registration and login
2. **Test API Functions**: Ensure all CRUD operations work
3. **Test File Upload**: Verify medical file storage works
4. **Build UI Components**: Start implementing the frontend interfaces

## Support

If you encounter issues:

1. Check the Supabase logs in your dashboard
2. Verify your environment variables are correct
3. Ensure your Supabase project has the necessary permissions
4. Review the migration files for any syntax errors

The database schema is now ready to support the full Doctory Healthcare Platform functionality!