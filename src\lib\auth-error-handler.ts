/**
 * Authentication Error Handler for Doctory Healthcare Platform
 * Handles authentication errors gracefully and provides recovery mechanisms
 */

'use client';

import { AuthError } from '@supabase/supabase-js';
import { createClient } from '@/lib/supabase';

export interface AuthErrorHandler {
  handleAuthError: (error: AuthError) => Promise<boolean>;
  clearAuthState: () => Promise<void>;
  isTokenError: (error: any) => boolean;
}

export const createAuthErrorHandler = (): AuthErrorHandler => {
  const supabase = createClient();

  const isTokenError = (error: any): boolean => {
    if (!error) return false;
    
    const tokenErrorMessages = [
      'Invalid Refresh Token',
      'Refresh Token Not Found',
      'JWT expired',
      'invalid_token',
      'token_expired',
      'refresh_token_not_found'
    ];
    
    const errorMessage = error.message || error.error_description || '';
    return tokenErrorMessages.some(msg => 
      errorMessage.toLowerCase().includes(msg.toLowerCase())
    );
  };

  const clearAuthState = async (): Promise<void> => {
    try {
      // Sign out from Supabase (this clears server-side session)
      await supabase.auth.signOut();
      
      // Clear all Supabase-related items from localStorage
      if (typeof window !== 'undefined') {
        const keysToRemove: string[] = [];
        
        // Find all Supabase-related keys
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('supabase') || 
            key.includes('sb-') ||
            key.includes('auth-token') ||
            key.includes('access-token') ||
            key.includes('refresh-token')
          )) {
            keysToRemove.push(key);
          }
        }
        
        // Remove all found keys
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        // Also clear sessionStorage
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (
            key.includes('supabase') || 
            key.includes('sb-') ||
            key.includes('auth-token')
          )) {
            sessionStorage.removeItem(key);
          }
        }
      }
      
      console.log('✅ Auth state cleared successfully');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  };

  const handleAuthError = async (error: AuthError): Promise<boolean> => {
    console.error('Auth error detected:', error);
    
    if (isTokenError(error)) {
      console.log('🔄 Token error detected, clearing auth state...');
      await clearAuthState();
      
      // Reload the page to restart the auth flow
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      
      return true; // Error was handled
    }
    
    return false; // Error was not handled
  };

  return {
    handleAuthError,
    clearAuthState,
    isTokenError
  };
};

// Global error handler instance
export const authErrorHandler = createAuthErrorHandler();

// Utility function to wrap auth operations with error handling
export const withAuthErrorHandling = async <T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T> => {
  try {
    return await operation();
  } catch (error: any) {
    const handled = await authErrorHandler.handleAuthError(error);
    
    if (handled) {
      // If error was handled (token error), return fallback or throw
      if (fallback !== undefined) {
        return fallback;
      }
      throw new Error('Authentication session expired. Please refresh the page.');
    }
    
    // Re-throw unhandled errors
    throw error;
  }
};
