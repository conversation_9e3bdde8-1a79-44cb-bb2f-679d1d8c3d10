'use client';

import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { StatusBadge } from '../ui/StatusBadge';
import { Appointment } from '@/types';

type AppointmentCardProps = {
  appointment: Appointment;
  onCancel?: (id: string) => void;
  onReschedule?: (id: string) => void;
  className?: string;
};

// Simple translations for this component
const translations = {
  cancel: 'Cancel',
  reschedule: 'Reschedule',
  date: 'Date',
  time: 'Time',
  doctor: 'Doctor',
  status: 'Status',
};

export const AppointmentCard: React.FC<AppointmentCardProps> = ({
  appointment,
  onCancel,
  onReschedule,
  className = ''
}) => {
  const t = (key: string) => translations[key as keyof typeof translations] || key;
  const [isLoading, setIsLoading] = useState(false);
  
  const handleCancel = async () => {
    if (!onCancel) return;
    
    setIsLoading(true);
    try {
      await onCancel(appointment.id);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={`bg-card rounded-lg shadow-sm overflow-hidden border border-border ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{appointment.date}</h3>
            <p className="text-sm text-muted-foreground">{appointment.time}</p>
          </div>
          
          <StatusBadge status={appointment.status} />
        </div>
        
        <div className="mt-4 pt-4 border-t border-border">
          {appointment.status === 'pending' || appointment.status === 'confirmed' ? (
            <div className="flex space-x-2 rtl:space-x-reverse">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleCancel}
                isLoading={isLoading}
                disabled={isLoading}
              >
                {t('cancel')}
              </Button>
              
              {appointment.status === 'confirmed' && onReschedule && (
                <Button 
                  variant="secondary" 
                  size="sm"
                  onClick={() => onReschedule(appointment.id)}
                >
                  {t('reschedule')}
                </Button>
              )}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
