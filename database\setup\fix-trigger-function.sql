-- Fix Trigger Function for Automatic Patient/Doctor Record Creation
-- This script fixes the handle_new_user() function to properly create
-- patient or doctor records based on the user's role

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- <PERSON><PERSON> improved function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
  user_email TEXT;
BEGIN
  -- Get the role from user metadata (passed during signup)
  user_role := COALESCE(NEW.raw_user_meta_data->>'role', 'patient');
  user_email := NEW.email;
  
  -- Insert into users table
  INSERT INTO public.users (id, email, role)
  VALUES (NEW.id, user_email, user_role);
  
  -- Create corresponding patient or doctor record
  IF user_role = 'patient' THEN
    INSERT INTO public.patients (user_id, name, phone)
    VALUES (
      NEW.id, 
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(user_email, '@', 1)), 
      COALESCE(NEW.raw_user_meta_data->>'phone', '')
    );
  ELSIF user_role = 'doctor' THEN
    INSERT INTO public.doctors (user_id, name, specialty, experience, bio)
    VALUES (
      NEW.id,
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(user_email, '@', 1)),
      COALESCE(NEW.raw_user_meta_data->>'specialty', 'General Medicine'),
      COALESCE((NEW.raw_user_meta_data->>'experience')::INTEGER, 0),
      COALESCE(NEW.raw_user_meta_data->>'bio', '')
    );
  END IF;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the auth user creation
    RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function on new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Test the trigger function
SELECT 'Trigger function updated successfully!' as message;

-- Verify the function exists
SELECT 
  proname as function_name,
  prosrc as function_body
FROM pg_proc 
WHERE proname = 'handle_new_user';
