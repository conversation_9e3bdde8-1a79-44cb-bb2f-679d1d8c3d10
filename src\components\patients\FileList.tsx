import React from 'react';
import { MedicalFile } from '@/types';

type FileListProps = {
  files: MedicalFile[];
  onDownload: (fileUrl: string, fileName: string) => void;
  className?: string;
};

// Simple translations for this component
const translations = {
  noFiles: 'No files uploaded yet',
  download: 'Download',
  uploadedOn: 'Uploaded on',
};

export const FileList: React.FC<FileListProps> = ({
  files,
  onDownload,
  className = ''
}) => {
  const t = (key: string) => translations[key as keyof typeof translations] || key;

  if (files.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('noFiles')}</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {files.map((file) => (
        <div 
          key={file.id}
          className="bg-card rounded-lg shadow-sm border border-border p-4 flex justify-between items-center"
        >
          <div>
            <p className="font-medium">{file.file_name}</p>
            <p className="text-sm text-muted-foreground">
              {new Date(file.created_at).toLocaleDateString()}
            </p>
          </div>
          
          <button
            onClick={() => onDownload(file.file_url, file.file_name)}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            {t('download')}
          </button>
        </div>
      ))}
    </div>
  );
};
