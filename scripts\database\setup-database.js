#!/usr/bin/env node

/**
 * Database Setup Script for Doctory Healthcare Platform
 * 
 * This script sets up the complete database schema including:
 * - All tables with proper relationships
 * - Row Level Security (RLS) policies
 * - Storage buckets and policies
 * - Sample data for testing
 * 
 * Usage: node scripts/setup-database.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration(filePath) {
  console.log(`📄 Running migration: ${path.basename(filePath)}`);
  
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`❌ Error in ${path.basename(filePath)}:`, error.message);
      return false;
    }
    
    console.log(`✅ Successfully applied: ${path.basename(filePath)}`);
    return true;
  } catch (err) {
    console.error(`❌ Failed to read or execute ${path.basename(filePath)}:`, err.message);
    return false;
  }
}

async function setupDatabase() {
  console.log('🚀 Starting Doctory database setup...\n');

  // Check if we can connect to Supabase
  try {
    const { data, error } = await supabase.from('_test').select('*').limit(1);
    if (error && !error.message.includes('relation "_test" does not exist')) {
      throw error;
    }
    console.log('✅ Connected to Supabase successfully\n');
  } catch (err) {
    console.error('❌ Failed to connect to Supabase:', err.message);
    process.exit(1);
  }

  // Run migrations in order
  const migrationFiles = [
    'supabase/migrations/20240102000000_complete_schema.sql',
    'supabase/migrations/20240102000001_storage_setup.sql'
  ];

  let allSuccessful = true;

  for (const file of migrationFiles) {
    const filePath = path.join(process.cwd(), file);
    
    if (!fs.existsSync(filePath)) {
      console.error(`❌ Migration file not found: ${file}`);
      allSuccessful = false;
      continue;
    }

    const success = await runMigration(filePath);
    if (!success) {
      allSuccessful = false;
    }
    
    console.log(''); // Add spacing between migrations
  }

  if (allSuccessful) {
    console.log('🎉 Database setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ All tables created with proper relationships');
    console.log('   ✅ Row Level Security (RLS) policies applied');
    console.log('   ✅ Storage bucket configured for medical files');
    console.log('   ✅ Sample data inserted for testing');
    console.log('\n🔗 Next steps:');
    console.log('   1. Test the authentication flow');
    console.log('   2. Verify doctor and patient registration');
    console.log('   3. Test appointment booking functionality');
  } else {
    console.log('❌ Database setup completed with errors');
    console.log('Please check the error messages above and fix any issues');
    process.exit(1);
  }
}

// Alternative method using direct SQL execution for environments without rpc
async function setupDatabaseDirect() {
  console.log('🚀 Starting Doctory database setup (direct method)...\n');

  const migrationFiles = [
    'supabase/migrations/20240102000000_complete_schema.sql',
    'supabase/migrations/20240102000001_storage_setup.sql'
  ];

  for (const file of migrationFiles) {
    const filePath = path.join(process.cwd(), file);
    
    if (!fs.existsSync(filePath)) {
      console.error(`❌ Migration file not found: ${file}`);
      continue;
    }

    console.log(`📄 Processing migration: ${path.basename(filePath)}`);
    
    try {
      const sql = fs.readFileSync(filePath, 'utf8');
      
      // Split SQL into individual statements
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
          if (error) {
            console.warn(`⚠️  Warning in statement: ${error.message}`);
          }
        }
      }
      
      console.log(`✅ Successfully processed: ${path.basename(filePath)}`);
    } catch (err) {
      console.error(`❌ Failed to process ${path.basename(filePath)}:`, err.message);
    }
    
    console.log('');
  }

  console.log('🎉 Database setup process completed!');
}

// Run the setup
if (require.main === module) {
  setupDatabase().catch(err => {
    console.error('❌ Setup failed:', err.message);
    console.log('\n💡 Trying alternative setup method...\n');
    setupDatabaseDirect().catch(altErr => {
      console.error('❌ Alternative setup also failed:', altErr.message);
      process.exit(1);
    });
  });
}

module.exports = { setupDatabase, setupDatabaseDirect };