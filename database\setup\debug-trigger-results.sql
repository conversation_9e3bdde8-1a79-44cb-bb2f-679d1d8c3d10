-- Debug Trigger Results
-- Run this to see if the trigger created the expected records

-- 1. Check what's in public.users table
SELECT '=== PUBLIC.USERS TABLE ===' as section;
SELECT id, email, role, created_at FROM public.users ORDER BY created_at DESC;

-- 2. Check what's in patients table  
SELECT '=== PATIENTS TABLE ===' as section;
SELECT id, user_id, name, phone, created_at FROM public.patients ORDER BY created_at DESC;

-- 3. Check what's in doctors table
SELECT '=== DOCTORS TABLE ===' as section;
SELECT id, user_id, name, specialty, experience, created_at FROM public.doctors ORDER BY created_at DESC;

-- 4. Check the relationship between auth.users and public.users
SELECT '=== USER RELATIONSHIPS ===' as section;
SELECT 
  au.email as auth_email,
  au.raw_user_meta_data->>'role' as metadata_role,
  pu.email as public_email,
  pu.role as public_role,
  CASE 
    WHEN pu.id IS NOT NULL THEN 'LINKED'
    ELSE 'MISSING_FROM_PUBLIC'
  END as status
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
ORDER BY au.created_at DESC;

-- 5. Check for any recent errors in the logs (if accessible)
SELECT '=== TRIGGER FUNCTION TEST ===' as section;

-- 6. Manual test of the trigger logic
DO $$
DECLARE
  test_email TEXT := '<EMAIL>';
  test_role TEXT;
  user_exists BOOLEAN;
BEGIN
  -- Check if user exists in auth.users
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = test_email) INTO user_exists;
  
  IF user_exists THEN
    -- Get the role from metadata
    SELECT raw_user_meta_data->>'role' INTO test_role 
    FROM auth.users 
    WHERE email = test_email;
    
    RAISE NOTICE 'User % exists with role: %', test_email, COALESCE(test_role, 'NULL');
    
    -- Check if they exist in public.users
    IF EXISTS(SELECT 1 FROM public.users WHERE email = test_email) THEN
      RAISE NOTICE 'User exists in public.users table';
    ELSE
      RAISE NOTICE 'User MISSING from public.users table - trigger may have failed';
    END IF;
    
    -- Check if patient record exists
    IF test_role = 'patient' THEN
      IF EXISTS(
        SELECT 1 FROM public.patients p 
        JOIN public.users u ON p.user_id = u.id 
        WHERE u.email = test_email
      ) THEN
        RAISE NOTICE 'Patient record exists';
      ELSE
        RAISE NOTICE 'Patient record MISSING - trigger may have failed';
      END IF;
    END IF;
    
  ELSE
    RAISE NOTICE 'User % does not exist in auth.users', test_email;
  END IF;
END $$;

-- 7. Check if there are any constraint violations or issues
SELECT '=== CONSTRAINT CHECK ===' as section;

-- Check for orphaned records
SELECT 'Orphaned patients (no user):' as check_type, COUNT(*) as count
FROM public.patients p
LEFT JOIN public.users u ON p.user_id = u.id
WHERE u.id IS NULL;

SELECT 'Orphaned doctors (no user):' as check_type, COUNT(*) as count  
FROM public.doctors d
LEFT JOIN public.users u ON d.user_id = u.id
WHERE u.id IS NULL;

-- Check for users without profiles
SELECT 'Patients without profiles:' as check_type, COUNT(*) as count
FROM public.users u
LEFT JOIN public.patients p ON u.id = p.user_id
WHERE u.role = 'patient' AND p.user_id IS NULL;

SELECT 'Doctors without profiles:' as check_type, COUNT(*) as count
FROM public.users u  
LEFT JOIN public.doctors d ON u.id = d.user_id
WHERE u.role = 'doctor' AND d.user_id IS NULL;
