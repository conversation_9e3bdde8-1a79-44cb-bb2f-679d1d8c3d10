#!/usr/bin/env node

/**
 * Simple Database Verification Script
 * Quick check of database connectivity and basic table structure
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Doctory Database Quick Verification');
console.log('=====================================');

if (!supabaseUrl || !supabaseKey) {
  console.log('❌ Missing environment variables:');
  console.log(`   SUPABASE_URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
  console.log(`   SUPABASE_KEY: ${supabaseKey ? '✅ Set' : '❌ Missing'}`);
  process.exit(1);
}

console.log('✅ Environment variables loaded');
console.log(`   URL: ${supabaseUrl}`);
console.log(`   Key: ${supabaseKey.substring(0, 20)}...`);

const supabase = createClient(supabaseUrl, supabaseKey);

async function quickCheck() {
  console.log('\n🔍 Testing database connection...');
  
  try {
    // Test basic connectivity with a simple query
    const { data, error } = await supabase
      .from('doctors')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Database connection failed:');
      console.log(`   Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      console.log(`   Details: ${error.details}`);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (err) {
    console.log('❌ Connection error:', err.message);
    return false;
  }
}

async function checkTables() {
  console.log('\n🔍 Checking table access...');
  
  const tables = ['users', 'patients', 'doctors', 'availability', 'appointments', 'medical_files'];
  const results = [];
  
  for (const table of tables) {
    try {
      const { error } = await supabase.from(table).select('*').limit(1);
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results.push(false);
      } else {
        console.log(`✅ ${table}: Accessible`);
        results.push(true);
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results.push(false);
    }
  }
  
  return results.every(r => r);
}

async function checkSampleData() {
  console.log('\n🔍 Checking sample data...');
  
  try {
    const { data: doctors, error } = await supabase
      .from('doctors')
      .select('name, specialty')
      .limit(5);
    
    if (error) {
      console.log(`❌ Sample data check failed: ${error.message}`);
      return false;
    }
    
    if (doctors && doctors.length > 0) {
      console.log(`✅ Found ${doctors.length} sample doctors:`);
      doctors.forEach(doc => console.log(`   - ${doc.name} (${doc.specialty})`));
      return true;
    } else {
      console.log('⚠️  No sample doctors found');
      return false;
    }
  } catch (err) {
    console.log(`❌ Sample data error: ${err.message}`);
    return false;
  }
}

async function runQuickVerification() {
  const results = {
    connection: await quickCheck(),
    tables: await checkTables(),
    sampleData: await checkSampleData()
  };
  
  console.log('\n📋 Quick Verification Results:');
  console.log('==============================');
  console.log(`Connection: ${results.connection ? '✅ Pass' : '❌ Fail'}`);
  console.log(`Tables: ${results.tables ? '✅ Pass' : '❌ Fail'}`);
  console.log(`Sample Data: ${results.sampleData ? '✅ Pass' : '⚠️  Warning'}`);
  
  if (results.connection && results.tables) {
    console.log('\n🎉 Database is ready for development!');
    if (!results.sampleData) {
      console.log('💡 Consider adding sample data for testing');
    }
  } else {
    console.log('\n❌ Database setup incomplete');
    console.log('Please run the database setup script');
  }
}

// Run if called directly
if (require.main === module) {
  runQuickVerification().catch(console.error);
}

module.exports = { runQuickVerification };