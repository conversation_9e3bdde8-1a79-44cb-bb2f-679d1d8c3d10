'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { createClient } from '@/lib/supabase';

export default function VerifyPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const handleVerificationResult = async () => {
      // Check if we have verification result from the callback
      if (searchParams.has('verified')) {
        const verified = searchParams.get('verified');
        if (verified === 'true') {
          setVerificationStatus('success');
          // Redirect to login after 3 seconds
          setTimeout(() => {
            router.push('/auth/login');
          }, 3000);
        } else {
          setVerificationStatus('error');
          setErrorMessage('Email verification failed. Please try again.');
        }
        return;
      }

      // Check if we have an error from the callback
      if (searchParams.has('error')) {
        const error = searchParams.get('error');
        setVerificationStatus('error');
        setErrorMessage(error || 'An error occurred during verification');
        return;
      }

      // If no specific parameters, check the current user status
      try {
        const supabase = createClient();
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error) {
          throw new Error(error.message);
        }

        if (user?.email_confirmed_at) {
          setVerificationStatus('success');
          // Redirect to login after 3 seconds
          setTimeout(() => {
            router.push('/auth/login');
          }, 3000);
        } else {
          // Show pending verification message
          setVerificationStatus('error');
          setErrorMessage('Email verification is pending. Please check your inbox and click the verification link.');
        }
      } catch (err: any) {
        setVerificationStatus('error');
        setErrorMessage(err.message || 'An error occurred during verification');
      }
    };

    handleVerificationResult();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-3xl font-bold text-primary">Doctory</h1>
          </Link>
          <p className="mt-2 text-muted-foreground">Email Verification</p>
        </div>
        
        <div className="max-w-md w-full mx-auto p-6 bg-card rounded-xl shadow-lg border border-border text-card-foreground">
          {verificationStatus === 'loading' && (
            <div className="text-center p-4">
              <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
              <p>Verifying your email...</p>
            </div>
          )}
          
          {verificationStatus === 'success' && (
            <div className="text-center">
              <div className="mb-4 text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4">Email Verified</h2>
              <p className="mb-6">
                Your email has been successfully verified. You will be redirected to the login page shortly.
              </p>
              <Link 
                href="/auth/login" 
                className="block w-full bg-primary text-primary-foreground py-2 rounded-md hover:bg-primary/90 transition-colors text-center"
              >
                Go to login
              </Link>
            </div>
          )}
          
          {verificationStatus === 'error' && (
            <div className="text-center">
              <div className="mb-4 text-red-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4">Verification Failed</h2>
              <p className="mb-6 text-red-600">
                {errorMessage || 'An error occurred during email verification. Please try again.'}
              </p>
              <div className="flex flex-col space-y-2">
                <Link 
                  href="/auth/login" 
                  className="block w-full bg-primary text-primary-foreground py-2 rounded-md hover:bg-primary/90 transition-colors text-center"
                >
                  Go to login
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 