"use client"

import React from 'react';
import Link from 'next/link';
import { Appointment, Doctor } from '@/types';
import { formatDate, formatTime } from '@/lib/utils';

type UpcomingAppointmentsProps = {
  appointments: (Appointment & { doctor?: Doctor })[];
};

// Simple translations for this component
const translations = {
  upcomingAppointments: 'Upcoming Appointments',
  noUpcomingAppointments: 'No upcoming appointments',
  findDoctor: 'Find a Doctor',
  viewDetails: 'View Details',
  pending: 'Pending',
  confirmed: 'Confirmed',
  cancelled: 'Cancelled',
};

export function UpcomingAppointments({ appointments }: UpcomingAppointmentsProps) {
  const t = (key: string) => translations[key as keyof typeof translations] || key;

  return (
    <div className="bg-card rounded-lg border border-border p-6 shadow-sm">
      <h2 className="text-xl font-semibold mb-4">{t('upcomingAppointments')}</h2>
      
      {appointments.length === 0 ? (
        <div className="text-center py-6">
          <p className="text-muted-foreground">{t('noUpcomingAppointments')}</p>
          <Link 
            href="/doctors" 
            className="mt-4 inline-block text-primary hover:underline"
          >
            {t('findDoctor')}
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {appointments.map((appointment) => {
            const doctor = appointment.doctor;
            return (
              <div 
                key={appointment.id} 
                className="flex items-center justify-between border-b border-border pb-4 last:border-0 last:pb-0"
              >
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-medium">
                      {doctor?.name?.substring(0, 2).toUpperCase() || 'DR'}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium">{doctor?.name || t('doctor')}</h3>
                    <p className="text-sm text-muted-foreground">{doctor?.specialty || t('specialist')}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{formatDate(appointment.date)}</div>
                  <div className="text-sm text-muted-foreground">{formatTime(appointment.time)}</div>
                </div>
              </div>
            );
          })}
          
          <div className="pt-4">
            <Link 
              href="/appointments" 
              className="text-primary hover:underline text-sm font-medium"
            >
              {t('viewAllAppointments')} →
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}