#!/usr/bin/env node

/**
 * Verify Database Setup for Doctory Healthcare Platform
 * This script checks if all tables and data are properly set up
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Doctory Database Verification');
console.log('================================\n');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifySetup() {
  console.log('🔍 Testing connection...');
  try {
    const { data, error } = await supabase.auth.getSession();
    console.log('✅ Connected to Supabase\n');
  } catch (err) {
    console.log('❌ Connection failed:', err.message);
    return;
  }

  console.log('📋 Checking database tables...');
  console.log('------------------------------');
  
  const tables = [
    { name: 'users', description: 'User accounts and roles' },
    { name: 'patients', description: 'Patient profiles' },
    { name: 'doctors', description: 'Doctor profiles and specialties' },
    { name: 'availability', description: 'Doctor availability schedules' },
    { name: 'appointments', description: 'Appointment bookings' },
    { name: 'medical_files', description: 'Medical file references' }
  ];

  let workingTables = 0;
  const tableStatus = {};

  for (const table of tables) {
    try {
      const { data, error, count } = await supabase
        .from(table.name)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`❌ ${table.name}: ${error.message}`);
        tableStatus[table.name] = { working: false, error: error.message };
      } else {
        console.log(`✅ ${table.name}: Working (${count || 0} records)`);
        tableStatus[table.name] = { working: true, count: count || 0 };
        workingTables++;
      }
    } catch (err) {
      console.log(`❌ ${table.name}: ${err.message}`);
      tableStatus[table.name] = { working: false, error: err.message };
    }
  }

  console.log(`\n📊 Tables Status: ${workingTables}/${tables.length} working\n`);

  // Check sample data
  if (tableStatus.doctors?.working) {
    console.log('🔍 Checking sample doctors...');
    try {
      const { data: doctors, error } = await supabase
        .from('doctors')
        .select('name, specialty, experience')
        .limit(10);

      if (error) {
        console.log(`❌ Sample doctors check failed: ${error.message}`);
      } else if (doctors && doctors.length > 0) {
        console.log(`✅ Found ${doctors.length} sample doctors:`);
        doctors.forEach(doc => {
          console.log(`   - ${doc.name} (${doc.specialty}, ${doc.experience} years)`);
        });
      } else {
        console.log('⚠️ No sample doctors found');
      }
    } catch (err) {
      console.log(`❌ Sample doctors error: ${err.message}`);
    }
    console.log('');
  }

  // Check availability data
  if (tableStatus.availability?.working) {
    console.log('🔍 Checking doctor availability...');
    try {
      const { data: availability, error } = await supabase
        .from('availability')
        .select('*')
        .limit(5);

      if (error) {
        console.log(`❌ Availability check failed: ${error.message}`);
      } else if (availability && availability.length > 0) {
        console.log(`✅ Found ${availability.length} availability records`);
      } else {
        console.log('⚠️ No availability records found');
      }
    } catch (err) {
      console.log(`❌ Availability error: ${err.message}`);
    }
    console.log('');
  }

  // Final assessment
  console.log('📋 Setup Assessment');
  console.log('===================');

  if (workingTables === tables.length) {
    console.log('🎉 Database setup is COMPLETE and WORKING!');
    console.log('\n✅ All systems ready:');
    console.log('   • All 6 tables are accessible');
    console.log('   • Sample data is available');
    console.log('   • Authentication system is ready');
    console.log('   • Application can start normally');
    
    console.log('\n🚀 Next steps:');
    console.log('   1. Your dev server: http://localhost:3001');
    console.log('   2. Test user registration');
    console.log('   3. Search for doctors');
    console.log('   4. Book appointments');
    console.log('   5. Upload medical files');
    
  } else if (workingTables > 0) {
    console.log('⚠️ Database setup is PARTIAL');
    console.log(`   ${workingTables}/${tables.length} tables are working`);
    console.log('\n💡 Missing tables need to be created manually');
    console.log('   Run: node supabase-direct-setup.js for instructions');
    
  } else {
    console.log('❌ Database setup is INCOMPLETE');
    console.log('   No tables are accessible');
    console.log('\n🔧 Required action:');
    console.log('   1. Run: node supabase-direct-setup.js');
    console.log('   2. Follow the manual setup instructions');
    console.log('   3. Execute the SQL in Supabase dashboard');
    console.log('   4. Run this verification again');
  }

  // Environment check
  console.log('\n📋 Environment Status');
  console.log('---------------------');
  console.log(`✅ Supabase URL: ${supabaseUrl}`);
  console.log(`✅ API Key: ${supabaseKey.substring(0, 20)}...`);
  console.log(`✅ Connection: Working`);
  
  return workingTables === tables.length;
}

// Execute verification
if (require.main === module) {
  verifySetup().then(success => {
    if (success) {
      console.log('\n🎯 VERIFICATION PASSED - Your database is ready!');
      process.exit(0);
    } else {
      console.log('\n⚠️ VERIFICATION INCOMPLETE - Some setup required');
      process.exit(1);
    }
  }).catch(err => {
    console.error('\n❌ Verification failed:', err.message);
    process.exit(1);
  });
}

module.exports = { verifySetup };