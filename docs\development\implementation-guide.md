# Implementation Guide - Enhanced Project Structure

This guide provides step-by-step instructions for implementing the enhanced project structure in the Doctory Healthcare Platform.

## Quick Start

### 1. Database Setup

Run the database setup scripts in Supabase SQL Editor:

```sql
-- 1. Run complete setup
-- Copy and paste: database/setup/complete-setup.sql

-- 2. Apply table policies
-- Copy and paste: database/policies/table-policies.sql

-- 3. Apply storage policies
-- Copy and paste: database/policies/storage-policies.sql

-- 4. Verify setup
-- Copy and paste: database/verification/verify-setup.sql
```

### 2. Update App Layout

Update your main layout to include the new providers:

```tsx
// src/app/layout.tsx
import { AuthProvider } from '@/providers/AuthProvider';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { LanguageProvider } from '@/providers/LanguageProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <LanguageProvider>
          <ThemeProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </ThemeProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
```

### 3. Update Components to Use New Hooks

Replace existing authentication logic:

```tsx
// Before
import { useUser } from '@supabase/auth-helpers-react';

// After
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const { user, loading, signOut } = useAuth();
  // ... rest of component
}
```

Replace theme logic:

```tsx
// Before
const [theme, setTheme] = useState('light');

// After
import { useTheme } from '@/hooks/useTheme';

function MyComponent() {
  const { theme, setTheme, toggleTheme } = useTheme();
  // ... rest of component
}
```

Replace hardcoded routes:

```tsx
// Before
<Link href="/patients/appointments">

// After
import { ROUTES } from '@/constants/routes';
<Link href={ROUTES.PATIENTS.APPOINTMENTS}>
```

### 4. Update API Calls

Replace hardcoded endpoints:

```tsx
// Before
const response = await fetch('/api/doctors');

// After
import { API_ENDPOINTS } from '@/constants/api-endpoints';
const response = await fetch(API_ENDPOINTS.DOCTORS.BASE);
```

## Detailed Implementation Steps

### Step 1: Authentication Integration

1. **Update existing auth components:**

```tsx
// src/components/auth/AuthForm.tsx
import { useAuth } from '@/hooks/useAuth';
import { ROUTES } from '@/constants/routes';

export const AuthForm = () => {
  const { signIn, signUp, loading } = useAuth();
  
  const handleSignIn = async (email: string, password: string) => {
    const { error } = await signIn(email, password);
    if (!error) {
      router.push(ROUTES.DASHBOARD);
    }
  };
  
  // ... rest of component
};
```

2. **Update protected route middleware:**

```tsx
// src/middleware.ts
import { PROTECTED_ROUTES, PUBLIC_ROUTES } from '@/constants/routes';

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  
  if (PROTECTED_ROUTES.some(route => path.startsWith(route))) {
    // Check authentication
  }
  
  // ... rest of middleware
}
```

### Step 2: Theme Integration

1. **Update theme toggle component:**

```tsx
// src/components/ui/ThemeToggle.tsx
import { useTheme } from '@/hooks/useTheme';
import { THEME_CONFIG } from '@/constants/themes';

export const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  
  return (
    <select 
      value={theme} 
      onChange={(e) => setTheme(e.target.value as Theme)}
    >
      {Object.entries(THEME_CONFIG).map(([key, config]) => (
        <option key={key} value={key}>
          {config.icon} {config.name}
        </option>
      ))}
    </select>
  );
};
```

2. **Update CSS variables in globals.css:**

```css
/* src/app/globals.css */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... other variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... other variables */
}

.innovayt {
  --background: 0 0% 100%;
  --foreground: 262 83% 15%;
  --primary: 262 83% 58%;
  /* ... other variables */
}
```

### Step 3: Language Integration

1. **Update language switcher:**

```tsx
// src/components/ui/LanguageSwitcher.tsx
import { useLanguage } from '@/hooks/useLanguage';
import { LANGUAGE_CONFIG } from '@/constants/languages';

export const LanguageSwitcher = () => {
  const { language, setLanguage, toggleLanguage, t } = useLanguage();
  
  return (
    <button onClick={toggleLanguage}>
      {LANGUAGE_CONFIG[language].flag} {LANGUAGE_CONFIG[language].nativeName}
    </button>
  );
};
```

2. **Update components to use translations:**

```tsx
// Before
<h1>Dashboard</h1>

// After
import { useLanguage } from '@/hooks/useLanguage';

function Dashboard() {
  const { t } = useLanguage();
  
  return <h1>{t('nav.dashboard')}</h1>;
}
```

### Step 4: Database Integration

1. **Update Supabase queries to use constants:**

```tsx
// Before
const { data } = await supabase.from('doctors').select('*');

// After
import { SUPABASE_ENDPOINTS } from '@/constants/api-endpoints';
const { data } = await supabase.from(SUPABASE_ENDPOINTS.TABLES.DOCTORS).select('*');
```

2. **Update file upload to use storage constants:**

```tsx
// Before
const { data } = await supabase.storage.from('medical-files').upload(path, file);

// After
import { SUPABASE_ENDPOINTS } from '@/constants/api-endpoints';
const { data } = await supabase.storage
  .from(SUPABASE_ENDPOINTS.STORAGE.MEDICAL_FILES)
  .upload(path, file);
```

## Testing the Implementation

### 1. Authentication Testing

```tsx
// Test authentication flow
const { user, signIn, signOut } = useAuth();

// Test sign in
await signIn('<EMAIL>', 'password');
expect(user).toBeTruthy();

// Test sign out
await signOut();
expect(user).toBeNull();
```

### 2. Theme Testing

```tsx
// Test theme switching
const { theme, setTheme } = useTheme();

setTheme('dark');
expect(document.documentElement.classList.contains('dark')).toBe(true);

setTheme('light');
expect(document.documentElement.classList.contains('light')).toBe(true);
```

### 3. Language Testing

```tsx
// Test language switching
const { language, setLanguage, isRTL, t } = useLanguage();

setLanguage('ar');
expect(isRTL).toBe(true);
expect(document.documentElement.getAttribute('dir')).toBe('rtl');

setLanguage('en');
expect(isRTL).toBe(false);
expect(document.documentElement.getAttribute('dir')).toBe('ltr');
```

## Troubleshooting

### Common Issues

1. **Hydration Mismatch**
   - Ensure providers check for `mounted` state before rendering
   - Use `visibility: hidden` during initial load

2. **TypeScript Errors**
   - Ensure all constants are properly typed with `as const`
   - Use proper type assertions for dynamic values

3. **Authentication Issues**
   - Verify Supabase configuration
   - Check RLS policies are properly applied
   - Ensure user roles are correctly set

4. **Theme Not Applying**
   - Check CSS variables are defined
   - Verify theme classes are added to document
   - Ensure Tailwind config includes theme classes

5. **Translation Missing**
   - Add missing translation keys to language constants
   - Provide fallback values for missing translations
   - Check language detection logic

## Performance Considerations

1. **Lazy Loading**
   - Use dynamic imports for large components
   - Implement code splitting for different user roles

2. **Memoization**
   - Use `useMemo` for expensive calculations
   - Use `useCallback` for event handlers

3. **State Management**
   - Keep context state minimal
   - Use local state when possible
   - Implement proper cleanup in useEffect

## Next Steps

1. **Enhance Testing**
   - Add comprehensive unit tests
   - Implement integration tests
   - Add E2E tests for critical flows

2. **Improve Performance**
   - Implement proper loading states
   - Add error boundaries
   - Optimize re-renders

3. **Add Features**
   - Implement notification system
   - Add real-time updates
   - Enhance accessibility

4. **Documentation**
   - Add API documentation
   - Create component documentation
   - Update deployment guides

This implementation guide provides a solid foundation for integrating the enhanced project structure into your Doctory Healthcare Platform.