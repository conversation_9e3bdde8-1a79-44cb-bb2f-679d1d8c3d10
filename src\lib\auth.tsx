'use client';
import { createContext, useContext, useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase';
import { User } from '@/types';

type AuthContextType = {
  user: User | null;
  loading: boolean;
  signUp: (email: string, password: string, role: 'patient' | 'doctor') => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    // Check active user and sets the user (secure method)
    const getUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (user && !error) {
        const { data: userData } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        setUser(userData as User);
      }

      setLoading(false);
    };

    getUser();

    // Listen for changes on auth state
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (session?.user) {
          const { data: userData } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();
            
          setUser(userData as User);
        } else {
          setUser(null);
        }
        
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signUp = async (email: string, password: string, role: 'patient' | 'doctor') => {
    try {
      // First, check if the email already exists in the users table
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('email')
        .eq('email', email)
        .maybeSingle();
      
      if (checkError) {
        console.error('Error checking existing user:', checkError);
        // If we can't check for existing users, it might be because the table doesn't exist
        // In this case, we'll proceed with the signup and let Supabase handle duplicates
      } else if (existingUser) {
        return { error: { message: 'Email already registered' } };
      }
      
      // Attempt to sign up the user with Supabase Auth with email confirmation
      // The trigger function will automatically create the user profile and patient/doctor record
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role,
            name: email.split('@')[0], // Default name from email
          },
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : ''}/api/auth/callback`,
        }
      });

      if (error) {
        console.error('Supabase Auth Error:', error);
        return { error };
      }

      if (!data.user) {
        return { error: { message: 'Failed to create user account' } };
      }

      // The trigger function handles creating the user profile and patient/doctor records automatically
      
      return { error: null };
    } catch (err) {
      console.error('Signup Process Error:', err);
      return { error: { 
        message: 'An unexpected error occurred during registration.', 
        details: err instanceof Error ? err.message : String(err) 
      }};
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, loading, signUp, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
