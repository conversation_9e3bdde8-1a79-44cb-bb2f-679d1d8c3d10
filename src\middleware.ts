import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';

// List of public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/verify',
];

// Role-based route permissions
const roleBasedRoutes = {
  '/dashboard': ['patient', 'doctor', 'admin'],
  '/profile': ['patient', 'doctor', 'admin'],
  '/appointments': ['patient', 'doctor', 'admin'],
  '/doctors': ['patient', 'admin'],
  '/patients': ['doctor', 'admin'],
};

export async function middleware(request: NextRequest) {
  // Temporarily disable middleware for debugging
  return NextResponse.next();

  // Initialize response
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Create supabase client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: (name) => request.cookies.get(name)?.value,
        set: (name, value, options) => {
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove: (name, options) => {
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Check if the route is public
  const pathname = request.nextUrl.pathname;

  if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'))) {
    return response;
  }

  // Get the user with error handling for token issues
  let user = null;
  let userError = null;

  try {
    const result = await supabase.auth.getUser();
    user = result.data.user;
    userError = result.error;
  } catch (error: any) {
    console.error('Middleware auth error:', error);

    // Check if this is a token-related error
    const isTokenError = error.message && (
      error.message.includes('Invalid Refresh Token') ||
      error.message.includes('Refresh Token Not Found') ||
      error.message.includes('JWT expired') ||
      error.code === 'refresh_token_not_found'
    );

    if (isTokenError) {
      // Clear auth cookies and redirect to login
      const authCookies = ['sb-access-token', 'sb-refresh-token'];
      authCookies.forEach(cookieName => {
        response.cookies.set({
          name: cookieName,
          value: '',
          expires: new Date(0),
          path: '/',
        });
      });

      // Redirect to login for protected routes
      if (!publicRoutes.includes(pathname)) {
        const redirectUrl = new URL('/auth/login', request.url);
        redirectUrl.searchParams.set('callbackUrl', encodeURI(request.url));
        return NextResponse.redirect(redirectUrl);
      }
    }

    userError = error;
  }

  // If no user and trying to access protected route, redirect to login
  if (!user && !publicRoutes.includes(pathname)) {
    const redirectUrl = new URL('/auth/login', request.url);
    redirectUrl.searchParams.set('callbackUrl', encodeURI(request.url));
    return NextResponse.redirect(redirectUrl);
  }

  // If we have a user but the route requires specific roles, check for permissions
  if (user && !userError) {
    // Check for role-based access
    for (const [route, allowedRoles] of Object.entries(roleBasedRoutes)) {
      if (pathname === route || pathname.startsWith(route + '/')) {
        // Fetch the user's role from the database
        const { data: userData, error } = await supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error || !userData || !allowedRoles.includes(userData.role)) {
          // Redirect to dashboard or appropriate error page if role doesn't have permission
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }
    }
  }

  return response;
}

// Specify which paths this middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - /images (public image files)
     */
    '/((?!_next/static|_next/image|favicon.ico|images).*)',
  ],
};