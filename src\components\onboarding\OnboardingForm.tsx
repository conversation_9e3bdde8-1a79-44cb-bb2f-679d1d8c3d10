'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { Input } from '@/components/ui/Input';

interface OnboardingFormProps {
  userRole: 'patient' | 'doctor';
}

export const OnboardingForm: React.FC<OnboardingFormProps> = ({ userRole }) => {
  const router = useRouter();
  const { user, refreshUser } = useAuth();
  const supabase = createClient();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Common fields
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');

  // Doctor-specific fields
  const [specialty, setSpecialty] = useState('');
  const [experience, setExperience] = useState('');
  const [bio, setBio] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    if (!user) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      if (userRole === 'patient') {
        // Update patient profile
        const { error: patientError } = await supabase
          .from('patients')
          .update({
            name: name.trim(),
            phone: phone.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id);

        if (patientError) {
          throw new Error(patientError.message);
        }
      } else if (userRole === 'doctor') {
        // Update doctor profile
        const { error: doctorError } = await supabase
          .from('doctors')
          .update({
            name: name.trim(),
            specialty: specialty.trim(),
            experience: parseInt(experience) || 0,
            bio: bio.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id);

        if (doctorError) {
          throw new Error(doctorError.message);
        }
      }

      // Refresh user data to update profile completion status
      await refreshUser();

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Onboarding error:', err);
      setError(err.message || 'Failed to complete profile setup');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary">Welcome to Doctory!</h1>
          <p className="mt-2 text-muted-foreground">
            Let's complete your {userRole} profile
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-md border">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Common Fields */}
            <Input
              id="name"
              type="text"
              label="Full Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              placeholder="Enter your full name"
            />

            <Input
              id="phone"
              type="tel"
              label="Phone Number"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              required
              placeholder="Enter your phone number"
            />

            {/* Doctor-specific Fields */}
            {userRole === 'doctor' && (
              <>
                <Input
                  id="specialty"
                  type="text"
                  label="Medical Specialty"
                  value={specialty}
                  onChange={(e) => setSpecialty(e.target.value)}
                  required
                  placeholder="e.g., Cardiology, Pediatrics, General Medicine"
                />

                <Input
                  id="experience"
                  type="number"
                  label="Years of Experience"
                  value={experience}
                  onChange={(e) => setExperience(e.target.value)}
                  required
                  min="0"
                  max="50"
                  placeholder="Years of medical practice"
                />

                <div className="space-y-2">
                  <label htmlFor="bio" className="block text-sm font-medium text-foreground">
                    Professional Bio
                  </label>
                  <textarea
                    id="bio"
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground"
                    placeholder="Brief description of your medical background and expertise..."
                    required
                  />
                </div>
              </>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Completing Profile...' : 'Complete Profile'}
            </button>
          </form>

          <div className="mt-4 text-center">
            <p className="text-sm text-muted-foreground">
              You can update your profile information later in your dashboard.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
