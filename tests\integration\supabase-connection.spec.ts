import { test, expect } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';

test.describe('Supabase Connection', () => {
  test('should connect to Supabase successfully', async () => {
    // Get environment variables from .env.local
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
    
    // Verify that the environment variables are set
    expect(supabaseUrl).not.toBe('');
    expect(supabaseAnonKey).not.toBe('');
    
    // Create a Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test a simple query to verify connection
    const { error } = await supabase.from('users').select('count', { count: 'exact', head: true });
    
    // Expect no error
    expect(error).toBeNull();
  });
  
  test('should have the required tables in the database', async () => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Check if the users table exists
    const { error: usersError } = await supabase.from('users').select('count', { count: 'exact', head: true });
    expect(usersError).toBeNull();
    
    // Check if the patients table exists
    const { error: patientsError } = await supabase.from('patients').select('count', { count: 'exact', head: true });
    expect(patientsError).toBeNull();
    
    // Check if the doctors table exists
    const { error: doctorsError } = await supabase.from('doctors').select('count', { count: 'exact', head: true });
    expect(doctorsError).toBeNull();
    
    // Check if the appointments table exists
    const { error: appointmentsError } = await supabase.from('appointments').select('count', { count: 'exact', head: true });
    expect(appointmentsError).toBeNull();
  });
});