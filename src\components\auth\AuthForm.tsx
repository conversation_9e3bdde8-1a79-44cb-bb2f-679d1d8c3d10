'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { Input } from '@/components/ui/Input';

type AuthFormProps = {
  type: 'login' | 'register';
};

// Simple fallback translations
const translations = {
  'auth.signIn': 'Sign In',
  'auth.signUp': 'Sign Up',
  'auth.email': 'Email',
  'auth.password': 'Password',
  'auth.forgotPassword': 'Forgot Password',
  'nav.patients': 'Patient',
  'nav.doctors': 'Doctor',
  'auth.roleSelection': 'I am a:',
};

export const AuthForm: React.FC<AuthFormProps> = ({ type }) => {
  const t = (key: string) => translations[key as keyof typeof translations] || key;
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';
  
  const { signIn, signUp } = useAuth();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<'patient' | 'doctor'>('patient');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (type === 'login') {
        const { error } = await signIn(email, password);
        if (error) {
          console.error('Login error:', error);
          throw new Error(error.message || 'Failed to sign in. Please check your credentials.');
        }
        // Note: Redirect will be handled by useEffect in layout based on user.needsOnboarding
        router.push(callbackUrl);
      } else {
        // Validate password strength
        if (password.length < 6) {
          throw new Error('Password must be at least 6 characters long');
        }
        
        const { error } = await signUp(email, password, role);
        if (error) {
          console.error('Registration error:', error);
          
          // Check for specific error messages
          if (error.message?.includes('already registered')) {
            throw new Error('This email is already registered. Please use a different email or sign in.');
          } else if (error.message?.includes('profile setup failed')) {
            throw new Error('Account created but profile setup failed. Please try signing in or contact support.');
          } else {
            throw new Error(error.message || 'Failed to create account. Please try again.');
          }
        }
        
        // Show verification message
        setVerificationSent(true);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (verificationSent) {
    return (
      <div className="max-w-md w-full mx-auto p-6 bg-card rounded-xl shadow-lg border border-border text-card-foreground">
        <div className="text-center">
          <div className="mb-4 text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4">Verification Email Sent</h2>
          <p className="mb-6">
            We've sent a verification email to <strong>{email}</strong>. 
            Please check your inbox and click the verification link to activate your account.
          </p>
          <Link 
            href="/auth/login" 
            className="block w-full bg-primary text-primary-foreground py-2 rounded-md hover:bg-primary/90 transition-colors text-center"
          >
            Go to login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md w-full mx-auto p-6 bg-card rounded-xl shadow-lg border border-border text-card-foreground">
      <h2 className="text-2xl font-bold text-center mb-6">
        {type === 'login' ? t('auth.signIn') : t('auth.signUp')}
      </h2>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          id="email"
          type="email"
          label={t('auth.email')}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />

        <Input
          id="password"
          type="password"
          label={t('auth.password')}
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        
        {type === 'register' && (
          <div>
            <label className="block text-sm font-medium mb-1">{t('auth.roleSelection')}</label>
            <div className="flex space-x-4 rtl:space-x-reverse">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="role"
                  value="patient"
                  checked={role === 'patient'}
                  onChange={() => setRole('patient')}
                  className="mr-2 rtl:ml-2 rtl:mr-0 accent-primary"
                />
                {t('nav.patients')}
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="role"
                  value="doctor"
                  checked={role === 'doctor'}
                  onChange={() => setRole('doctor')}
                  className="mr-2 rtl:ml-2 rtl:mr-0 accent-primary"
                />
                {t('nav.doctors')}
              </label>
            </div>
          </div>
        )}
        
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-primary text-primary-foreground py-2 rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50"
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : type === 'login' ? t('auth.signIn') : t('auth.signUp')}
        </button>
      </form>

      <div className="mt-4 text-center text-sm">
        {type === 'login' ? (
          <>
            Don't have an account?{' '}
            <Link href="/auth/register" className="text-primary hover:underline">
              {t('auth.signUp')}
            </Link>
          </>
        ) : (
          <>
            Already have an account?{' '}
            <Link href="/auth/login" className="text-primary hover:underline">
              {t('auth.signIn')}
            </Link>
          </>
        )}
      </div>

      {type === 'login' && (
        <div className="mt-2 text-center">
          <Link href="/auth/forgot-password" className="text-sm text-primary hover:underline">
            {t('auth.forgotPassword')}
          </Link>
        </div>
      )}
    </div>
  );
};